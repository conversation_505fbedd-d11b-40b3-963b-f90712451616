<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DailyTask extends Model
{
    use HasFactory;

    protected $fillable = [
        'campus_id',
        'title',
        'description',
        'instructions',
        'points_reward',
        'task_date',
        'difficulty',
        'is_active',
    ];

    protected $casts = [
        'campus_id' => 'integer',
        'points_reward' => 'integer',
        'task_date' => 'date',
        'is_active' => 'boolean',
    ];

    /**
     * Get the campus that owns the daily task.
     */
    public function campus(): BelongsTo
    {
        return $this->belongsTo(Campus::class);
    }

    /**
     * Get the submissions for the daily task.
     */
    public function submissions(): HasMany
    {
        return $this->hasMany(TaskSubmission::class);
    }

    /**
     * Scope a query to only include active tasks.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include today's tasks.
     */
    public function scopeToday($query)
    {
        return $query->where('task_date', today());
    }
}
