* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body.auth-body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
}

.auth-container {
    width: 100%;
    max-width: 400px;
    padding: 20px;
}

.auth-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 40px;
    backdrop-filter: blur(10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-header h1 {
    font-size: 28px;
    font-weight: bold;
    color: #FFD700;
    margin-bottom: 10px;
    letter-spacing: 2px;
}

.auth-header p {
    color: #cccccc;
    font-size: 16px;
}

.auth-form {
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #ffffff;
    font-weight: 500;
    font-size: 14px;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"] {
    width: 100%;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 16px;
    transition: all 0.3s ease;
}

.form-group input[type="text"]:focus,
.form-group input[type="email"]:focus,
.form-group input[type="password"]:focus {
    outline: none;
    border-color: #FFD700;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.form-group input::placeholder {
    color: #999999;
}

.checkbox-group {
    display: flex;
    align-items: center;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: #cccccc;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 10px;
    width: 18px;
    height: 18px;
}

.auth-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    border: none;
    border-radius: 8px;
    color: #000000;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.auth-btn:hover {
    background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(255, 215, 0, 0.3);
}

.auth-footer {
    text-align: center;
    margin-top: 20px;
}

.auth-footer p {
    margin-bottom: 10px;
    color: #cccccc;
    font-size: 14px;
}

.auth-footer a {
    color: #FFD700;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.auth-footer a:hover {
    color: #FFA500;
    text-decoration: underline;
}

.error {
    color: #ff4444;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

@media (max-width: 480px) {
    .auth-card {
        padding: 30px 20px;
    }
    
    .auth-header h1 {
        font-size: 24px;
    }
}
