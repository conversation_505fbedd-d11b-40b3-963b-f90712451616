<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Campus;
use App\Models\ChatRoom;

class ChatRoomSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $campuses = Campus::all();

        foreach ($campuses as $campus) {
            // General chat room
            ChatRoom::create([
                'campus_id' => $campus->id,
                'name' => 'General Chat',
                'slug' => 'general-chat',
                'description' => 'General discussion and networking for ' . $campus->name . ' students.',
                'type' => 'general',
                'is_active' => true,
                'sort_order' => 1,
            ]);

            // Announcements room
            ChatRoom::create([
                'campus_id' => $campus->id,
                'name' => 'Announcements',
                'slug' => 'announcements',
                'description' => 'Important updates and announcements from professors.',
                'type' => 'announcements',
                'is_active' => true,
                'sort_order' => 2,
            ]);

            // Help & Support room
            ChatRoom::create([
                'campus_id' => $campus->id,
                'name' => 'Help & Support',
                'slug' => 'help-support',
                'description' => 'Get help with your questions and technical issues.',
                'type' => 'help',
                'is_active' => true,
                'sort_order' => 3,
            ]);

            // Wins & Success Stories room
            ChatRoom::create([
                'campus_id' => $campus->id,
                'name' => 'Wins & Success Stories',
                'slug' => 'wins-success',
                'description' => 'Share your victories and celebrate success with the community.',
                'type' => 'wins',
                'is_active' => true,
                'sort_order' => 4,
            ]);
        }
    }
}
