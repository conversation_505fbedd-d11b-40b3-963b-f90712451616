<?php $__env->startSection('title', 'Admin Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="admin-dashboard">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <h1>Admin Dashboard</h1>
        <p>Platform overview and management center</p>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stat-card users">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo e(number_format($stats['total_users'])); ?></h3>
                <p>Total Users</p>
                <span class="stat-change positive"><?php echo e($stats['active_users']); ?> active</span>
            </div>
        </div>

        <div class="stat-card campuses">
            <div class="stat-icon">
                <i class="fas fa-university"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo e($stats['total_campuses']); ?></h3>
                <p>Campuses</p>
                <span class="stat-change"><?php echo e($stats['total_enrollments']); ?> enrollments</span>
            </div>
        </div>

        <div class="stat-card courses">
            <div class="stat-icon">
                <i class="fas fa-book"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo e($stats['total_courses']); ?></h3>
                <p>Courses</p>
                <span class="stat-change"><?php echo e($stats['total_lessons']); ?> lessons</span>
            </div>
        </div>

        <div class="stat-card tasks">
            <div class="stat-icon">
                <i class="fas fa-tasks"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo e($stats['pending_tasks']); ?></h3>
                <p>Pending Reviews</p>
                <span class="stat-change urgent">Needs attention</span>
            </div>
        </div>

        <div class="stat-card messages">
            <div class="stat-icon">
                <i class="fas fa-comments"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo e(number_format($stats['total_messages'])); ?></h3>
                <p>Total Messages</p>
                <span class="stat-change">Community activity</span>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="dashboard-grid">
        <!-- Recent Users -->
        <div class="dashboard-card">
            <div class="card-header">
                <h2>Recent Users</h2>
                <a href="<?php echo e(route('admin.users')); ?>" class="view-all">View All</a>
            </div>
            <div class="users-list">
                <?php $__currentLoopData = $recentUsers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="user-item">
                        <div class="user-avatar">
                            <?php if($user->avatar): ?>
                                <img src="<?php echo e(Storage::url($user->avatar)); ?>" alt="<?php echo e($user->name); ?>">
                            <?php else: ?>
                                <div class="default-avatar"><?php echo e(substr($user->name, 0, 1)); ?></div>
                            <?php endif; ?>
                        </div>
                        <div class="user-info">
                            <h4><?php echo e($user->name); ?></h4>
                            <p><?php echo e($user->email); ?></p>
                            <span class="user-role <?php echo e($user->role); ?>"><?php echo e(ucfirst($user->role)); ?></span>
                        </div>
                        <div class="user-stats">
                            <span class="points"><?php echo e($user->points); ?> pts</span>
                            <span class="join-date"><?php echo e($user->created_at->diffForHumans()); ?></span>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>

        <!-- Pending Task Reviews -->
        <div class="dashboard-card">
            <div class="card-header">
                <h2>Pending Task Reviews</h2>
                <a href="<?php echo e(route('admin.task-submissions')); ?>" class="view-all">View All</a>
            </div>
            <div class="submissions-list">
                <?php $__currentLoopData = $recentSubmissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $submission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="submission-item">
                        <div class="submission-info">
                            <h4><?php echo e($submission->dailyTask->title); ?></h4>
                            <p><?php echo e($submission->user->name); ?> • <?php echo e($submission->dailyTask->campus->name); ?></p>
                            <span class="submission-date"><?php echo e($submission->created_at->diffForHumans()); ?></span>
                        </div>
                        <div class="submission-actions">
                            <a href="<?php echo e(route('admin.task-submissions')); ?>?submission=<?php echo e($submission->id); ?>" class="btn-review">
                                Review
                            </a>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                
                <?php if($recentSubmissions->isEmpty()): ?>
                    <div class="empty-state">
                        <i class="fas fa-check-circle"></i>
                        <p>No pending reviews</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Campus Statistics -->
        <div class="dashboard-card full-width">
            <div class="card-header">
                <h2>Campus Performance</h2>
            </div>
            <div class="campus-stats">
                <?php $__currentLoopData = $campusStats; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $campus): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="campus-stat-item">
                        <div class="campus-header">
                            <div class="campus-icon" style="color: <?php echo e($campus->color); ?>">
                                <i class="<?php echo e($campus->icon); ?>"></i>
                            </div>
                            <h4><?php echo e($campus->name); ?></h4>
                        </div>
                        <div class="campus-metrics">
                            <div class="metric">
                                <span class="metric-value"><?php echo e($campus->users_count); ?></span>
                                <span class="metric-label">Students</span>
                            </div>
                            <div class="metric">
                                <span class="metric-value"><?php echo e($campus->courses_count); ?></span>
                                <span class="metric-label">Courses</span>
                            </div>
                            <div class="metric">
                                <span class="metric-value"><?php echo e($campus->daily_tasks_count); ?></span>
                                <span class="metric-label">Tasks</span>
                            </div>
                        </div>
                        <div class="campus-progress">
                            <?php
                                $maxUsers = $campusStats->max('users_count');
                                $progressWidth = $maxUsers > 0 ? ($campus->users_count / $maxUsers) * 100 : 0;
                            ?>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: <?php echo e($progressWidth); ?>%; background: <?php echo e($campus->color); ?>"></div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>

        <!-- User Growth Chart -->
        <div class="dashboard-card full-width">
            <div class="card-header">
                <h2>User Growth (Last 30 Days)</h2>
            </div>
            <div class="chart-container">
                <canvas id="userGrowthChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <h2>Quick Actions</h2>
        <div class="actions-grid">
            <a href="<?php echo e(route('admin.users')); ?>" class="action-card">
                <i class="fas fa-users"></i>
                <h3>Manage Users</h3>
                <p>View and manage user accounts</p>
            </a>
            <a href="<?php echo e(route('admin.content')); ?>" class="action-card">
                <i class="fas fa-edit"></i>
                <h3>Content Management</h3>
                <p>Manage courses and lessons</p>
            </a>
            <a href="<?php echo e(route('admin.task-submissions')); ?>" class="action-card">
                <i class="fas fa-clipboard-check"></i>
                <h3>Review Tasks</h3>
                <p>Review pending submissions</p>
            </a>
            <a href="<?php echo e(route('leaderboard.index')); ?>" class="action-card">
                <i class="fas fa-trophy"></i>
                <h3>View Leaderboard</h3>
                <p>Check platform rankings</p>
            </a>
        </div>
    </div>
</div>

<style>
.admin-dashboard {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.dashboard-header {
    margin-bottom: 30px;
}

.dashboard-header h1 {
    color: #FFD700;
    font-size: 2.5rem;
    margin-bottom: 8px;
}

.dashboard-header p {
    color: #cccccc;
    font-size: 1.1rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    border-color: #FFD700;
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #000000;
}

.stat-card.users .stat-icon { background: #4CAF50; }
.stat-card.campuses .stat-icon { background: #2196F3; }
.stat-card.courses .stat-icon { background: #FF9800; }
.stat-card.tasks .stat-icon { background: #f44336; }
.stat-card.messages .stat-icon { background: #9C27B0; }

.stat-content h3 {
    color: #FFD700;
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-content p {
    color: #ffffff;
    margin-bottom: 8px;
    font-weight: 500;
}

.stat-change {
    font-size: 0.8rem;
    padding: 3px 8px;
    border-radius: 10px;
}

.stat-change.positive {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
}

.stat-change.urgent {
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.dashboard-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 25px;
}

.dashboard-card.full-width {
    grid-column: 1 / -1;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header h2 {
    color: #FFD700;
    font-size: 1.3rem;
}

.view-all {
    color: #FFD700;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
}

.view-all:hover {
    text-decoration: underline;
}

.users-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.user-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.user-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #FFD700;
    flex-shrink: 0;
}

.user-avatar img,
.default-avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.default-avatar {
    background: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.user-info {
    flex: 1;
}

.user-info h4 {
    color: #ffffff;
    margin-bottom: 3px;
    font-size: 0.95rem;
}

.user-info p {
    color: #cccccc;
    font-size: 0.8rem;
    margin-bottom: 5px;
}

.user-role {
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: bold;
    text-transform: uppercase;
}

.user-role.student { background: rgba(76, 175, 80, 0.2); color: #4CAF50; }
.user-role.professor { background: rgba(33, 150, 243, 0.2); color: #2196F3; }
.user-role.admin { background: rgba(244, 67, 54, 0.2); color: #f44336; }

.user-stats {
    text-align: right;
    font-size: 0.8rem;
}

.points {
    color: #FFD700;
    font-weight: bold;
    display: block;
    margin-bottom: 3px;
}

.join-date {
    color: #999;
}

.submissions-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.submission-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.submission-info h4 {
    color: #ffffff;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.submission-info p {
    color: #cccccc;
    font-size: 0.8rem;
    margin-bottom: 3px;
}

.submission-date {
    color: #999;
    font-size: 0.7rem;
}

.btn-review {
    background: #FFD700;
    color: #000000;
    padding: 6px 15px;
    border-radius: 15px;
    text-decoration: none;
    font-size: 0.8rem;
    font-weight: bold;
    transition: all 0.3s ease;
}

.btn-review:hover {
    background: #FFA500;
    transform: translateY(-1px);
}

.empty-state {
    text-align: center;
    padding: 30px;
    color: #666;
}

.empty-state i {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #4CAF50;
}

.campus-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.campus-stat-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.campus-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.campus-icon {
    font-size: 1.5rem;
}

.campus-header h4 {
    color: #ffffff;
    font-size: 1rem;
}

.campus-metrics {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.metric {
    text-align: center;
}

.metric-value {
    display: block;
    color: #FFD700;
    font-size: 1.2rem;
    font-weight: bold;
}

.metric-label {
    color: #cccccc;
    font-size: 0.7rem;
}

.campus-progress {
    margin-top: 10px;
}

.progress-bar {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    height: 6px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    border-radius: 5px;
    transition: width 0.3s ease;
}

.chart-container {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quick-actions h2 {
    color: #FFD700;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.action-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s ease;
}

.action-card:hover {
    transform: translateY(-5px);
    border-color: #FFD700;
    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.1);
}

.action-card i {
    font-size: 2.5rem;
    color: #FFD700;
    margin-bottom: 15px;
}

.action-card h3 {
    color: #ffffff;
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.action-card p {
    color: #cccccc;
    font-size: 0.9rem;
    margin: 0;
}

@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .campus-stats {
        grid-template-columns: 1fr;
    }
    
    .actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // User Growth Chart
    const ctx = document.getElementById('userGrowthChart');
    if (ctx) {
        const userGrowthData = <?php echo json_encode($userGrowth, 15, 512) ?>;
        
        // Simple chart implementation (you can replace with Chart.js)
        const canvas = ctx.getContext('2d');
        canvas.fillStyle = '#FFD700';
        canvas.fillRect(50, 50, 300, 100);
        canvas.fillStyle = '#ffffff';
        canvas.font = '14px Arial';
        canvas.fillText('User Growth Chart', 150, 110);
        canvas.fillStyle = '#cccccc';
        canvas.font = '12px Arial';
        canvas.fillText('(Chart.js integration recommended)', 120, 130);
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\New folder\the-real-world\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>