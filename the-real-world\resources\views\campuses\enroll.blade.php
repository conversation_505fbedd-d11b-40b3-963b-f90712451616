@extends('layouts.dashboard')

@section('title', 'Join ' . $campus->name)

@section('content')
<div class="enrollment-page">
    <div class="enrollment-container">
        <!-- Campus Header -->
        <div class="campus-header" style="background: linear-gradient(135deg, {{ $campus->color }}20 0%, {{ $campus->color }}10 100%)">
            <div class="campus-icon" style="color: {{ $campus->color }}">
                <i class="{{ $campus->icon }}"></i>
            </div>
            <h1>{{ $campus->name }}</h1>
            <p>{{ $campus->description }}</p>
        </div>

        <!-- Enrollment Content -->
        <div class="enrollment-content">
            <div class="enrollment-info">
                <h2>What You'll Learn</h2>
                <div class="learning-outcomes">
                    @switch($campus->slug)
                        @case('ecommerce')
                            <div class="outcome-item">
                                <i class="fas fa-check"></i>
                                <span>Product research and validation techniques</span>
                            </div>
                            <div class="outcome-item">
                                <i class="fas fa-check"></i>
                                <span>Store setup and optimization</span>
                            </div>
                            <div class="outcome-item">
                                <i class="fas fa-check"></i>
                                <span>Facebook and Google Ads mastery</span>
                            </div>
                            <div class="outcome-item">
                                <i class="fas fa-check"></i>
                                <span>Scaling strategies for 6-figure stores</span>
                            </div>
                            @break
                        @case('copywriting')
                            <div class="outcome-item">
                                <i class="fas fa-check"></i>
                                <span>Psychology of persuasive writing</span>
                            </div>
                            <div class="outcome-item">
                                <i class="fas fa-check"></i>
                                <span>Email marketing campaigns</span>
                            </div>
                            <div class="outcome-item">
                                <i class="fas fa-check"></i>
                                <span>Sales page optimization</span>
                            </div>
                            <div class="outcome-item">
                                <i class="fas fa-check"></i>
                                <span>Client acquisition strategies</span>
                            </div>
                            @break
                        @case('crypto')
                            <div class="outcome-item">
                                <i class="fas fa-check"></i>
                                <span>Technical analysis and chart reading</span>
                            </div>
                            <div class="outcome-item">
                                <i class="fas fa-check"></i>
                                <span>DeFi protocols and yield farming</span>
                            </div>
                            <div class="outcome-item">
                                <i class="fas fa-check"></i>
                                <span>Risk management strategies</span>
                            </div>
                            <div class="outcome-item">
                                <i class="fas fa-check"></i>
                                <span>Portfolio diversification</span>
                            </div>
                            @break
                        @default
                            <div class="outcome-item">
                                <i class="fas fa-check"></i>
                                <span>Industry-specific strategies and techniques</span>
                            </div>
                            <div class="outcome-item">
                                <i class="fas fa-check"></i>
                                <span>Practical implementation methods</span>
                            </div>
                            <div class="outcome-item">
                                <i class="fas fa-check"></i>
                                <span>Advanced optimization tactics</span>
                            </div>
                            <div class="outcome-item">
                                <i class="fas fa-check"></i>
                                <span>Scaling and growth strategies</span>
                            </div>
                    @endswitch
                </div>

                <div class="campus-features">
                    <h3>Campus Features</h3>
                    <div class="features-grid">
                        <div class="feature-item">
                            <i class="fas fa-video"></i>
                            <div>
                                <h4>{{ $campus->courses()->published()->count() }} Courses</h4>
                                <p>Comprehensive video tutorials</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-comments"></i>
                            <div>
                                <h4>{{ $campus->chatRooms()->active()->count() }} Chat Rooms</h4>
                                <p>24/7 community support</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-tasks"></i>
                            <div>
                                <h4>Daily Tasks</h4>
                                <p>Practical challenges</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-users"></i>
                            <div>
                                <h4>{{ $campus->users()->count() }} Students</h4>
                                <p>Active learning community</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enrollment Form -->
            <div class="enrollment-form">
                <div class="form-card">
                    <h3>Ready to Start?</h3>
                    <p>Join {{ number_format($campus->users()->count()) }} students already learning in this campus.</p>
                    
                    <form method="POST" action="{{ route('campuses.process-enrollment', $campus) }}">
                        @csrf
                        
                        <div class="enrollment-agreement">
                            <label class="checkbox-label">
                                <input type="checkbox" name="agree_terms" required>
                                <span class="checkmark"></span>
                                I agree to the terms and conditions and commit to actively participate in this campus.
                            </label>
                        </div>

                        <button type="submit" class="btn-enroll" style="background: linear-gradient(135deg, {{ $campus->color }} 0%, {{ $campus->color }}dd 100%)">
                            <i class="fas fa-rocket"></i>
                            Join {{ $campus->name }} Now
                        </button>
                    </form>

                    <div class="enrollment-guarantee">
                        <i class="fas fa-shield-alt"></i>
                        <div>
                            <h4>Success Guarantee</h4>
                            <p>Follow our proven system and see results, or get personalized coaching to ensure your success.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.enrollment-page {
    max-width: 1200px;
    margin: 0 auto;
}

.enrollment-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.campus-header {
    text-align: center;
    padding: 60px 40px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.campus-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.campus-header h1 {
    color: #FFD700;
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.campus-header p {
    color: #cccccc;
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

.enrollment-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    padding: 40px;
}

.enrollment-info h2 {
    color: #FFD700;
    font-size: 1.8rem;
    margin-bottom: 25px;
}

.learning-outcomes {
    margin-bottom: 40px;
}

.outcome-item {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    color: #ffffff;
}

.outcome-item i {
    color: #4CAF50;
    font-size: 1.1rem;
}

.campus-features h3 {
    color: #FFD700;
    font-size: 1.5rem;
    margin-bottom: 20px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.feature-item i {
    font-size: 1.5rem;
    color: #FFD700;
}

.feature-item h4 {
    color: #ffffff;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.feature-item p {
    color: #cccccc;
    font-size: 0.8rem;
}

.enrollment-form {
    position: sticky;
    top: 20px;
}

.form-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 30px;
    text-align: center;
}

.form-card h3 {
    color: #FFD700;
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.form-card > p {
    color: #cccccc;
    margin-bottom: 25px;
}

.enrollment-agreement {
    margin-bottom: 25px;
    text-align: left;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    cursor: pointer;
    color: #cccccc;
    font-size: 0.9rem;
    line-height: 1.4;
}

.checkbox-label input[type="checkbox"] {
    margin-top: 2px;
}

.btn-enroll {
    width: 100%;
    padding: 15px 25px;
    border: none;
    border-radius: 25px;
    color: #ffffff;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 25px;
}

.btn-enroll:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.enrollment-guarantee {
    display: flex;
    align-items: center;
    gap: 15px;
    text-align: left;
    padding: 20px;
    background: rgba(76, 175, 80, 0.1);
    border: 1px solid rgba(76, 175, 80, 0.3);
    border-radius: 10px;
}

.enrollment-guarantee i {
    color: #4CAF50;
    font-size: 1.5rem;
}

.enrollment-guarantee h4 {
    color: #4CAF50;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.enrollment-guarantee p {
    color: #cccccc;
    font-size: 0.8rem;
    line-height: 1.4;
}

@media (max-width: 768px) {
    .enrollment-content {
        grid-template-columns: 1fr;
    }
    
    .campus-header {
        padding: 40px 20px;
    }
    
    .enrollment-content {
        padding: 20px;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
}
</style>
@endsection
