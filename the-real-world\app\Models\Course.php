<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Course extends Model
{
    use HasFactory;

    protected $fillable = [
        'campus_id',
        'title',
        'slug',
        'description',
        'thumbnail',
        'duration_minutes',
        'difficulty',
        'is_published',
        'sort_order',
    ];

    protected $casts = [
        'campus_id' => 'integer',
        'duration_minutes' => 'integer',
        'is_published' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the campus that owns the course.
     */
    public function campus(): BelongsTo
    {
        return $this->belongsTo(Campus::class);
    }

    /**
     * Get the lessons for the course.
     */
    public function lessons(): HasMany
    {
        return $this->hasMany(Lesson::class);
    }

    /**
     * Scope a query to only include published courses.
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }
}
