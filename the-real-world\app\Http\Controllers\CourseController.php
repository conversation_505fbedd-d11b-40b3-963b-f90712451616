<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Campus;
use App\Models\Course;
use App\Models\Lesson;
use App\Models\UserProgress;
use Illuminate\Support\Facades\Auth;

class CourseController extends Controller
{
    /**
     * Display the specified course.
     */
    public function show(Campus $campus, Course $course)
    {
        // Check if user is enrolled in the campus
        if (!Auth::user()->campuses()->where('campus_id', $campus->id)->exists()) {
            return redirect()->route('campuses.enroll', $campus)->with('error', 'You need to enroll in this campus first.');
        }

        // Check if course belongs to campus
        if ($course->campus_id !== $campus->id) {
            abort(404);
        }

        $lessons = $course->lessons()->published()->orderBy('sort_order')->get();

        // Get user's progress for each lesson
        $userProgress = Auth::user()->progress()
            ->whereIn('lesson_id', $lessons->pluck('id'))
            ->get()
            ->keyBy('lesson_id');

        // Calculate course progress
        $totalLessons = $lessons->count();
        $completedLessons = $userProgress->where('is_completed', true)->count();
        $progressPercentage = $totalLessons > 0 ? round(($completedLessons / $totalLessons) * 100) : 0;

        return view('courses.show', compact('campus', 'course', 'lessons', 'userProgress', 'progressPercentage'));
    }

    /**
     * Display the specified lesson.
     */
    public function showLesson(Campus $campus, Course $course, Lesson $lesson)
    {
        // Check if user is enrolled in the campus
        if (!Auth::user()->campuses()->where('campus_id', $campus->id)->exists()) {
            return redirect()->route('campuses.enroll', $campus)->with('error', 'You need to enroll in this campus first.');
        }

        // Check if lesson belongs to course and course belongs to campus
        if ($lesson->course_id !== $course->id || $course->campus_id !== $campus->id) {
            abort(404);
        }

        // Get or create user progress for this lesson
        $userProgress = UserProgress::firstOrCreate([
            'user_id' => Auth::id(),
            'lesson_id' => $lesson->id,
        ], [
            'progress_percentage' => 0,
            'is_completed' => false,
            'time_spent_seconds' => 0,
        ]);

        // Get next and previous lessons
        $nextLesson = $course->lessons()
            ->published()
            ->where('sort_order', '>', $lesson->sort_order)
            ->orderBy('sort_order')
            ->first();

        $previousLesson = $course->lessons()
            ->published()
            ->where('sort_order', '<', $lesson->sort_order)
            ->orderBy('sort_order', 'desc')
            ->first();

        return view('lessons.show', compact('campus', 'course', 'lesson', 'userProgress', 'nextLesson', 'previousLesson'));
    }

    /**
     * Update lesson progress.
     */
    public function updateProgress(Request $request, Campus $campus, Course $course, Lesson $lesson)
    {
        $request->validate([
            'progress_percentage' => 'required|integer|min:0|max:100',
            'time_spent' => 'required|integer|min:0',
        ]);

        $userProgress = UserProgress::updateOrCreate([
            'user_id' => Auth::id(),
            'lesson_id' => $lesson->id,
        ], [
            'progress_percentage' => $request->progress_percentage,
            'is_completed' => $request->progress_percentage >= 100,
            'completed_at' => $request->progress_percentage >= 100 ? now() : null,
            'time_spent_seconds' => $request->time_spent,
        ]);

        // Award points if lesson is completed for the first time
        if ($userProgress->is_completed && $userProgress->wasRecentlyCreated) {
            $user = Auth::user();
            $user->increment('points', 25); // Award 25 points for lesson completion
        }

        return response()->json([
            'success' => true,
            'progress' => $userProgress,
            'message' => $userProgress->is_completed ? 'Lesson completed! +25 points' : 'Progress saved',
        ]);
    }
}
