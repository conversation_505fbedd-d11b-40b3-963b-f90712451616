<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Campus;
use App\Models\Course;
use App\Models\ChatRoom;
use App\Models\DailyTask;
use Illuminate\Support\Facades\Auth;

class CampusController extends Controller
{
    /**
     * Display a listing of all campuses.
     */
    public function index()
    {
        $campuses = Campus::active()->orderBy('sort_order')->get();
        return view('campuses.index', compact('campuses'));
    }

    /**
     * Display the specified campus.
     */
    public function show(Campus $campus)
    {
        // Check if user is enrolled in this campus
        $isEnrolled = Auth::user()->campuses()->where('campus_id', $campus->id)->exists();

        if (!$isEnrolled) {
            return redirect()->route('campuses.enroll', $campus)->with('info', 'You need to enroll in this campus first.');
        }

        // Get campus data
        $courses = $campus->courses()->published()->orderBy('sort_order')->get();
        $chatRooms = $campus->chatRooms()->active()->orderBy('sort_order')->get();
        $todayTask = $campus->dailyTasks()->today()->active()->first();

        // Get user's progress in this campus
        $userProgress = $this->getUserProgress($campus);

        return view('campuses.show', compact('campus', 'courses', 'chatRooms', 'todayTask', 'userProgress'));
    }

    /**
     * Show enrollment page for a campus.
     */
    public function enroll(Campus $campus)
    {
        // Check if already enrolled
        $isEnrolled = Auth::user()->campuses()->where('campus_id', $campus->id)->exists();

        if ($isEnrolled) {
            return redirect()->route('campuses.show', $campus);
        }

        return view('campuses.enroll', compact('campus'));
    }

    /**
     * Process campus enrollment.
     */
    public function processEnrollment(Request $request, Campus $campus)
    {
        $user = Auth::user();

        // Check if already enrolled
        if ($user->campuses()->where('campus_id', $campus->id)->exists()) {
            return redirect()->route('campuses.show', $campus)->with('info', 'You are already enrolled in this campus.');
        }

        // Enroll user in campus
        $user->campuses()->attach($campus->id, [
            'enrolled_at' => now(),
            'is_active' => true,
        ]);

        return redirect()->route('campuses.show', $campus)->with('success', 'Welcome to ' . $campus->name . '! Start your learning journey now.');
    }

    /**
     * Get user's progress in a campus.
     */
    private function getUserProgress(Campus $campus)
    {
        $user = Auth::user();
        $totalLessons = 0;
        $completedLessons = 0;

        foreach ($campus->courses as $course) {
            $courseLessons = $course->lessons()->published()->count();
            $totalLessons += $courseLessons;

            $completedCourseLessons = $user->progress()
                ->whereHas('lesson.course', function ($query) use ($course) {
                    $query->where('id', $course->id);
                })
                ->where('is_completed', true)
                ->count();

            $completedLessons += $completedCourseLessons;
        }

        $progressPercentage = $totalLessons > 0 ? round(($completedLessons / $totalLessons) * 100) : 0;

        return [
            'total_lessons' => $totalLessons,
            'completed_lessons' => $completedLessons,
            'progress_percentage' => $progressPercentage,
        ];
    }
}
