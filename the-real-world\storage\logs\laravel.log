[2025-07-13 23:06:04] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'courses' already exists (Connection: mysql, SQL: create table `courses` (`id` bigint unsigned not null auto_increment primary key, `campus_id` bigint unsigned not null, `title` varchar(255) not null, `slug` varchar(255) not null, `description` text not null, `thumbnail` varchar(255) null, `duration_minutes` int null, `difficulty` enum('beginner', 'intermediate', 'advanced') not null default 'beginner', `is_published` tinyint(1) not null default '0', `sort_order` int not null default '0', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'courses' already exists (Connection: mysql, SQL: create table `courses` (`id` bigint unsigned not null auto_increment primary key, `campus_id` bigint unsigned not null, `title` varchar(255) not null, `slug` varchar(255) not null, `description` text not null, `thumbnail` varchar(255) null, `duration_minutes` int null, `difficulty` enum('beginner', 'intermediate', 'advanced') not null default 'beginner', `is_published` tinyint(1) not null default '0', `sort_order` int not null default '0', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('create table `c...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('create table `c...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('create table `c...')
#3 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('courses', Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\database\\migrations\\2025_07_13_230259_create_courses_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2025_07_13_2302...', Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_13_2302...', Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\...', 3, false)
#15 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'courses' already exists at C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `c...', Array)
#2 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('create table `c...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('create table `c...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('create table `c...')
#5 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('courses', Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\database\\migrations\\2025_07_13_230259_create_courses_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2025_07_13_2302...', Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_13_2302...', Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\...', 3, false)
#17 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#27 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Desktop\\New folder\\the-real-world\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 {main}
"} 
