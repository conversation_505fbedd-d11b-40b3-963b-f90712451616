@extends('layouts.admin')

@section('title', 'User Management')

@section('content')
<div class="users-management">
    <!-- Page Header -->
    <div class="page-header">
        <div class="header-content">
            <h1>User Management</h1>
            <p>Manage platform users, roles, and permissions</p>
        </div>
        <div class="header-actions">
            <button class="btn btn-primary" onclick="exportUsers()">
                <i class="fas fa-download"></i>
                Export Users
            </button>
        </div>
    </div>

    <!-- Filters -->
    <div class="filters-section">
        <form method="GET" class="filters-form">
            <div class="filter-group">
                <input type="text" name="search" placeholder="Search users..." value="{{ request('search') }}" class="form-control">
            </div>
            <div class="filter-group">
                <select name="role" class="form-control">
                    <option value="">All Roles</option>
                    <option value="student" {{ request('role') === 'student' ? 'selected' : '' }}>Student</option>
                    <option value="professor" {{ request('role') === 'professor' ? 'selected' : '' }}>Professor</option>
                    <option value="admin" {{ request('role') === 'admin' ? 'selected' : '' }}>Admin</option>
                </select>
            </div>
            <div class="filter-group">
                <select name="status" class="form-control">
                    <option value="">All Status</option>
                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                    <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                </select>
            </div>
            <button type="submit" class="btn btn-secondary">
                <i class="fas fa-search"></i>
                Filter
            </button>
            <a href="{{ route('admin.users') }}" class="btn btn-secondary">
                <i class="fas fa-times"></i>
                Clear
            </a>
        </form>
    </div>

    <!-- Users Table -->
    <div class="table-container">
        <table class="table">
            <thead>
                <tr>
                    <th>User</th>
                    <th>Email</th>
                    <th>Role</th>
                    <th>Status</th>
                    <th>Campuses</th>
                    <th>Progress</th>
                    <th>Joined</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach($users as $user)
                    <tr class="user-row">
                        <td>
                            <div class="user-cell">
                                <div class="user-avatar">
                                    @if($user->avatar)
                                        <img src="{{ Storage::url($user->avatar) }}" alt="{{ $user->name }}">
                                    @else
                                        <div class="default-avatar">{{ substr($user->name, 0, 1) }}</div>
                                    @endif
                                </div>
                                <div class="user-info">
                                    <h4>{{ $user->name }}</h4>
                                    <p>@{{ $user->username }}</p>
                                    <span class="user-points">{{ number_format($user->points) }} pts</span>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="user-email">{{ $user->email }}</span>
                        </td>
                        <td>
                            <span class="role-badge {{ $user->role }}">
                                {{ ucfirst($user->role) }}
                            </span>
                        </td>
                        <td>
                            <span class="status-badge {{ $user->is_active ? 'active' : 'inactive' }}">
                                {{ $user->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </td>
                        <td>
                            <span class="stat-number">{{ $user->campuses_count }}</span>
                        </td>
                        <td>
                            <div class="progress-info">
                                <span class="progress-stat">{{ $user->task_submissions_count }} tasks</span>
                                <span class="progress-stat">{{ $user->progress_count }} lessons</span>
                            </div>
                        </td>
                        <td>
                            <span class="join-date">{{ $user->created_at->format('M d, Y') }}</span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="{{ route('admin.user-show', $user) }}" class="btn-action view" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button class="btn-action edit" onclick="editUser({{ $user->id }})" title="Edit User">
                                    <i class="fas fa-edit"></i>
                                </button>
                                @if($user->id !== Auth::id())
                                    <button class="btn-action {{ $user->is_active ? 'suspend' : 'activate' }}" 
                                            onclick="toggleUserStatus({{ $user->id }}, {{ $user->is_active ? 'false' : 'true' }})"
                                            title="{{ $user->is_active ? 'Suspend' : 'Activate' }} User">
                                        <i class="fas fa-{{ $user->is_active ? 'ban' : 'check' }}"></i>
                                    </button>
                                @endif
                            </div>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>

        @if($users->isEmpty())
            <div class="empty-state">
                <i class="fas fa-users"></i>
                <h3>No Users Found</h3>
                <p>No users match your current filters.</p>
            </div>
        @endif
    </div>

    <!-- Pagination -->
    @if($users->hasPages())
        <div class="pagination-wrapper">
            {{ $users->appends(request()->query())->links() }}
        </div>
    @endif
</div>

<!-- Edit User Modal -->
<div id="editUserModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Edit User</h3>
            <button class="modal-close" onclick="closeModal()">&times;</button>
        </div>
        <form id="editUserForm" method="POST">
            @csrf
            @method('PUT')
            <div class="modal-body">
                <div class="form-group">
                    <label for="edit_role">Role</label>
                    <select id="edit_role" name="role" class="form-control" required>
                        <option value="student">Student</option>
                        <option value="professor">Professor</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit_status">Status</label>
                    <select id="edit_status" name="is_active" class="form-control" required>
                        <option value="1">Active</option>
                        <option value="0">Inactive</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                <button type="submit" class="btn btn-primary">Save Changes</button>
            </div>
        </form>
    </div>
</div>

<style>
.users-management {
    max-width: 1400px;
    margin: 0 auto;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-content h1 {
    color: #FFD700;
    font-size: 2rem;
    margin-bottom: 5px;
}

.header-content p {
    color: #cccccc;
    font-size: 1rem;
}

.filters-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.filters-form {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.table-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.user-cell {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #FFD700;
    flex-shrink: 0;
}

.user-avatar img,
.default-avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.default-avatar {
    background: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 0.9rem;
}

.user-info h4 {
    color: #ffffff;
    margin-bottom: 2px;
    font-size: 0.9rem;
}

.user-info p {
    color: #cccccc;
    font-size: 0.8rem;
    margin-bottom: 2px;
}

.user-points {
    color: #FFD700;
    font-size: 0.7rem;
    font-weight: bold;
}

.user-email {
    color: #cccccc;
    font-size: 0.9rem;
}

.role-badge {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: bold;
    text-transform: uppercase;
}

.role-badge.student { background: rgba(76, 175, 80, 0.2); color: #4CAF50; }
.role-badge.professor { background: rgba(33, 150, 243, 0.2); color: #2196F3; }
.role-badge.admin { background: rgba(244, 67, 54, 0.2); color: #f44336; }

.status-badge {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: bold;
    text-transform: uppercase;
}

.status-badge.active { background: rgba(76, 175, 80, 0.2); color: #4CAF50; }
.status-badge.inactive { background: rgba(158, 158, 158, 0.2); color: #9e9e9e; }

.stat-number {
    color: #FFD700;
    font-weight: bold;
    font-size: 1rem;
}

.progress-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.progress-stat {
    color: #cccccc;
    font-size: 0.8rem;
}

.join-date {
    color: #999;
    font-size: 0.8rem;
}

.action-buttons {
    display: flex;
    gap: 5px;
}

.btn-action {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.btn-action.view {
    background: rgba(33, 150, 243, 0.2);
    color: #2196F3;
}

.btn-action.edit {
    background: rgba(255, 152, 0, 0.2);
    color: #FF9800;
}

.btn-action.suspend {
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
}

.btn-action.activate {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
}

.btn-action:hover {
    transform: scale(1.1);
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #444;
}

.empty-state h3 {
    color: #ffffff;
    margin-bottom: 10px;
}

.pagination-wrapper {
    margin-top: 25px;
    display: flex;
    justify-content: center;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: #1a1a1a;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
    color: #FFD700;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: #cccccc;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .filters-form {
        flex-direction: column;
    }
    
    .filter-group {
        min-width: auto;
    }
    
    .table-container {
        overflow-x: auto;
    }
    
    .user-cell {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}
</style>

<script>
function editUser(userId) {
    // In a real implementation, you would fetch user data via AJAX
    const modal = document.getElementById('editUserModal');
    const form = document.getElementById('editUserForm');
    
    form.action = `/admin/users/${userId}/status`;
    modal.style.display = 'flex';
}

function closeModal() {
    document.getElementById('editUserModal').style.display = 'none';
}

function toggleUserStatus(userId, newStatus) {
    if (confirm(`Are you sure you want to ${newStatus === 'true' ? 'activate' : 'suspend'} this user?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/users/${userId}/status`;
        
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'PUT';
        
        const tokenField = document.createElement('input');
        tokenField.type = 'hidden';
        tokenField.name = '_token';
        tokenField.value = csrfToken;
        
        const statusField = document.createElement('input');
        statusField.type = 'hidden';
        statusField.name = 'is_active';
        statusField.value = newStatus;
        
        const roleField = document.createElement('input');
        roleField.type = 'hidden';
        roleField.name = 'role';
        roleField.value = 'student'; // Default role
        
        form.appendChild(methodField);
        form.appendChild(tokenField);
        form.appendChild(statusField);
        form.appendChild(roleField);
        
        document.body.appendChild(form);
        form.submit();
    }
}

function exportUsers() {
    // In a real implementation, this would trigger a CSV/Excel export
    alert('Export functionality would be implemented here');
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modal = document.getElementById('editUserModal');
    if (event.target === modal) {
        closeModal();
    }
});
</script>
@endsection
