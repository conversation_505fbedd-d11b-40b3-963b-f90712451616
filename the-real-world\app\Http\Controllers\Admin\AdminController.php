<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Campus;
use App\Models\Course;
use App\Models\Lesson;
use App\Models\DailyTask;
use App\Models\TaskSubmission;
use App\Models\Message;
use App\Models\UserProgress;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            if (auth()->user()->role !== 'admin') {
                abort(403, 'Access denied. Admin privileges required.');
            }
            return $next($request);
        });
    }

    /**
     * Display the admin dashboard.
     */
    public function dashboard()
    {
        // Get platform statistics
        $stats = [
            'total_users' => User::count(),
            'active_users' => User::where('is_active', true)->count(),
            'total_campuses' => Campus::count(),
            'total_courses' => Course::count(),
            'total_lessons' => Lesson::count(),
            'pending_tasks' => TaskSubmission::where('status', 'pending')->count(),
            'total_messages' => Message::count(),
            'total_enrollments' => DB::table('user_campus_enrollments')->count(),
        ];

        // Get recent activity
        $recentUsers = User::latest()->limit(5)->get();
        $recentSubmissions = TaskSubmission::with(['user', 'dailyTask.campus'])
            ->where('status', 'pending')
            ->latest()
            ->limit(10)
            ->get();

        // Get user growth data (last 30 days)
        $userGrowth = User::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->where('created_at', '>=', Carbon::now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Get campus enrollment stats
        $campusStats = Campus::withCount(['users', 'courses', 'dailyTasks'])
            ->orderBy('users_count', 'desc')
            ->get();

        return view('admin.dashboard', compact('stats', 'recentUsers', 'recentSubmissions', 'userGrowth', 'campusStats'));
    }

    /**
     * Display user management.
     */
    public function users(Request $request)
    {
        $query = User::query();

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('username', 'like', "%{$search}%");
            });
        }

        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $users = $query->withCount(['campuses', 'taskSubmissions', 'progress'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.users.index', compact('users'));
    }

    /**
     * Show user details.
     */
    public function userShow(User $user)
    {
        $user->load(['campuses', 'taskSubmissions.dailyTask.campus', 'progress.lesson.course.campus']);

        $userStats = [
            'total_points' => $user->points,
            'level' => $user->level,
            'enrolled_campuses' => $user->campuses->count(),
            'completed_lessons' => $user->progress()->where('is_completed', true)->count(),
            'completed_tasks' => $user->taskSubmissions()->where('status', 'approved')->count(),
            'pending_tasks' => $user->taskSubmissions()->where('status', 'pending')->count(),
            'join_date' => $user->created_at,
            'last_active' => $user->updated_at,
        ];

        return view('admin.users.show', compact('user', 'userStats'));
    }

    /**
     * Update user status.
     */
    public function updateUserStatus(Request $request, User $user)
    {
        $request->validate([
            'is_active' => 'required|boolean',
            'role' => 'required|in:student,professor,admin',
        ]);

        $user->update([
            'is_active' => $request->is_active,
            'role' => $request->role,
        ]);

        return back()->with('success', 'User status updated successfully.');
    }

    /**
     * Display content management.
     */
    public function content()
    {
        $campuses = Campus::withCount(['courses', 'users', 'dailyTasks'])->get();
        $recentCourses = Course::with('campus')->latest()->limit(10)->get();
        $recentLessons = Lesson::with('course.campus')->latest()->limit(10)->get();

        return view('admin.content.index', compact('campuses', 'recentCourses', 'recentLessons'));
    }

    /**
     * Display task submissions for review.
     */
    public function taskSubmissions(Request $request)
    {
        $query = TaskSubmission::with(['user', 'dailyTask.campus']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('campus')) {
            $query->whereHas('dailyTask', function($q) use ($request) {
                $q->where('campus_id', $request->campus);
            });
        }

        $submissions = $query->orderBy('created_at', 'desc')->paginate(20);
        $campuses = Campus::all();

        return view('admin.tasks.index', compact('submissions', 'campuses'));
    }

    /**
     * Review task submission.
     */
    public function reviewTaskSubmission(Request $request, TaskSubmission $submission)
    {
        $request->validate([
            'status' => 'required|in:approved,rejected',
            'feedback' => 'nullable|string|max:1000',
            'points_awarded' => 'nullable|integer|min:0|max:100',
        ]);

        $submission->update([
            'status' => $request->status,
            'feedback' => $request->feedback,
            'points_awarded' => $request->status === 'approved' ? ($request->points_awarded ?? $submission->dailyTask->points_reward) : 0,
            'reviewed_at' => now(),
            'reviewed_by' => auth()->id(),
        ]);

        // Award points to user if approved
        if ($request->status === 'approved') {
            $submission->user->increment('points', $submission->points_awarded);
        }

        return back()->with('success', 'Task submission reviewed successfully.');
    }
}
