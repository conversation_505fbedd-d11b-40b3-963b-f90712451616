<?php $__env->startSection('title', 'Campuses'); ?>

<?php $__env->startSection('content'); ?>
<div class="campuses-page">
    <!-- Header Section -->
    <div class="page-header">
        <h1>Choose Your Campus</h1>
        <p>Select a campus to start your journey to financial freedom. Each campus teaches proven methods to make money online.</p>
    </div>

    <!-- Campuses Grid -->
    <div class="campuses-grid">
        <?php $__currentLoopData = $campuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $campus): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="campus-card" style="border-color: <?php echo e($campus->color); ?>">
                <div class="campus-header" style="background: linear-gradient(135deg, <?php echo e($campus->color); ?>20 0%, <?php echo e($campus->color); ?>10 100%)">
                    <div class="campus-icon" style="color: <?php echo e($campus->color); ?>">
                        <i class="<?php echo e($campus->icon); ?>"></i>
                    </div>
                    <h3><?php echo e($campus->name); ?></h3>
                </div>
                
                <div class="campus-content">
                    <p><?php echo e($campus->description); ?></p>
                    
                    <div class="campus-stats">
                        <div class="stat">
                            <span class="stat-number"><?php echo e($campus->courses()->published()->count()); ?></span>
                            <span class="stat-label">Courses</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number"><?php echo e($campus->chatRooms()->active()->count()); ?></span>
                            <span class="stat-label">Chat Rooms</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number"><?php echo e($campus->users()->count()); ?></span>
                            <span class="stat-label">Students</span>
                        </div>
                    </div>
                </div>
                
                <div class="campus-footer">
                    <?php if(Auth::user()->campuses()->where('campus_id', $campus->id)->exists()): ?>
                        <a href="<?php echo e(route('campuses.show', $campus)); ?>" class="btn-campus enrolled">
                            <i class="fas fa-check"></i>
                            Enter Campus
                        </a>
                        <span class="enrolled-badge">Enrolled</span>
                    <?php else: ?>
                        <a href="<?php echo e(route('campuses.enroll', $campus)); ?>" class="btn-campus">
                            <i class="fas fa-plus"></i>
                            Join Campus
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    <!-- Campus Benefits -->
    <div class="campus-benefits">
        <h2>What You Get in Every Campus</h2>
        <div class="benefits-grid">
            <div class="benefit-item">
                <i class="fas fa-video"></i>
                <h4>Exclusive Video Content</h4>
                <p>Step-by-step tutorials and masterclasses</p>
            </div>
            <div class="benefit-item">
                <i class="fas fa-comments"></i>
                <h4>Live Chat Support</h4>
                <p>24/7 community and professor assistance</p>
            </div>
            <div class="benefit-item">
                <i class="fas fa-tasks"></i>
                <h4>Daily Challenges</h4>
                <p>Practical tasks to accelerate your learning</p>
            </div>
            <div class="benefit-item">
                <i class="fas fa-certificate"></i>
                <h4>Completion Certificates</h4>
                <p>Prove your expertise with official certificates</p>
            </div>
        </div>
    </div>
</div>

<style>
.campuses-page {
    max-width: 1200px;
    margin: 0 auto;
}

.page-header {
    text-align: center;
    margin-bottom: 50px;
}

.page-header h1 {
    color: #FFD700;
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.page-header p {
    color: #cccccc;
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

.campuses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.campus-card {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.campus-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.campus-header {
    padding: 30px 25px;
    text-align: center;
    position: relative;
}

.campus-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.campus-header h3 {
    color: #ffffff;
    font-size: 1.4rem;
    margin: 0;
}

.campus-content {
    padding: 0 25px 25px;
}

.campus-content p {
    color: #cccccc;
    line-height: 1.6;
    margin-bottom: 25px;
}

.campus-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 25px;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: #FFD700;
}

.stat-label {
    font-size: 0.8rem;
    color: #999;
}

.campus-footer {
    padding: 25px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.btn-campus {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: #000000;
    padding: 12px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    flex: 1;
    justify-content: center;
}

.btn-campus:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
}

.btn-campus.enrolled {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: #ffffff;
}

.enrolled-badge {
    background: #4CAF50;
    color: #ffffff;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    margin-left: 15px;
}

.campus-benefits {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 40px;
    text-align: center;
}

.campus-benefits h2 {
    color: #FFD700;
    font-size: 2rem;
    margin-bottom: 30px;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.benefit-item {
    text-align: center;
}

.benefit-item i {
    font-size: 2.5rem;
    color: #FFD700;
    margin-bottom: 15px;
}

.benefit-item h4 {
    color: #ffffff;
    margin-bottom: 10px;
}

.benefit-item p {
    color: #cccccc;
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .campuses-grid {
        grid-template-columns: 1fr;
    }
    
    .campus-stats {
        flex-direction: column;
        gap: 15px;
    }
    
    .campus-footer {
        flex-direction: column;
        gap: 15px;
    }
    
    .enrolled-badge {
        margin-left: 0;
    }
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\New folder\the-real-world\resources\views/campuses/index.blade.php ENDPATH**/ ?>