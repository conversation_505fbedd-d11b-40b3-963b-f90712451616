@extends('layouts.dashboard')

@section('title', 'Edit Profile')

@section('content')
<div class="edit-profile-page">
    <!-- Page Header -->
    <div class="page-header">
        <div class="breadcrumb">
            <a href="{{ route('profile.show') }}">Profile</a>
            <i class="fas fa-chevron-right"></i>
            <span>Edit Profile</span>
        </div>
        <h1>Edit Profile</h1>
        <p>Update your profile information and settings.</p>
    </div>

    <div class="edit-content">
        <!-- Profile Information -->
        <div class="edit-section">
            <div class="section-header">
                <h2>Profile Information</h2>
                <p>Update your basic profile details.</p>
            </div>
            
            <form method="POST" action="{{ route('profile.update') }}" enctype="multipart/form-data" class="profile-form">
                @csrf
                @method('PUT')
                
                <!-- Avatar Upload -->
                <div class="form-group avatar-upload">
                    <label>Profile Picture</label>
                    <div class="avatar-section">
                        <div class="current-avatar">
                            @if($user->avatar)
                                <img src="{{ Storage::url($user->avatar) }}" alt="{{ $user->name }}" id="avatarPreview">
                            @else
                                <div class="default-avatar" id="avatarPreview">{{ substr($user->name, 0, 1) }}</div>
                            @endif
                        </div>
                        <div class="avatar-controls">
                            <input type="file" id="avatar" name="avatar" accept="image/*" style="display: none;">
                            <button type="button" class="btn-upload" onclick="document.getElementById('avatar').click()">
                                <i class="fas fa-camera"></i>
                                Change Photo
                            </button>
                            <p class="upload-hint">JPG, PNG or GIF. Max size 2MB.</p>
                        </div>
                    </div>
                    @error('avatar')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Basic Information -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="name">Full Name</label>
                        <input type="text" id="name" name="name" value="{{ old('name', $user->name) }}" required>
                        @error('name')
                            <span class="error-message">{{ $message }}</span>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" name="username" value="{{ old('username', $user->username) }}" required>
                        @error('username')
                            <span class="error-message">{{ $message }}</span>
                        @enderror
                    </div>
                </div>

                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" value="{{ old('email', $user->email) }}" required>
                    @error('email')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="bio">Bio</label>
                    <textarea id="bio" name="bio" rows="4" placeholder="Tell us about yourself...">{{ old('bio', $user->bio) }}</textarea>
                    @error('bio')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="location">Location</label>
                        <input type="text" id="location" name="location" value="{{ old('location', $user->location) }}" placeholder="City, Country">
                        @error('location')
                            <span class="error-message">{{ $message }}</span>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="website">Website</label>
                        <input type="url" id="website" name="website" value="{{ old('website', $user->website) }}" placeholder="https://yourwebsite.com">
                        @error('website')
                            <span class="error-message">{{ $message }}</span>
                        @enderror
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn-save">
                        <i class="fas fa-save"></i>
                        Save Changes
                    </button>
                    <a href="{{ route('profile.show') }}" class="btn-cancel">Cancel</a>
                </div>
            </form>
        </div>

        <!-- Password Change -->
        <div class="edit-section">
            <div class="section-header">
                <h2>Change Password</h2>
                <p>Update your password to keep your account secure.</p>
            </div>
            
            <form method="POST" action="{{ route('profile.update-password') }}" class="password-form">
                @csrf
                @method('PUT')
                
                <div class="form-group">
                    <label for="current_password">Current Password</label>
                    <input type="password" id="current_password" name="current_password" required>
                    @error('current_password')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="password">New Password</label>
                    <input type="password" id="password" name="password" required>
                    @error('password')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="password_confirmation">Confirm New Password</label>
                    <input type="password" id="password_confirmation" name="password_confirmation" required>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn-save">
                        <i class="fas fa-lock"></i>
                        Update Password
                    </button>
                </div>
            </form>
        </div>

        <!-- Account Settings -->
        <div class="edit-section">
            <div class="section-header">
                <h2>Account Settings</h2>
                <p>Manage your account preferences and privacy settings.</p>
            </div>
            
            <div class="settings-grid">
                <div class="setting-item">
                    <div class="setting-info">
                        <h4>Email Notifications</h4>
                        <p>Receive notifications about your progress and updates</p>
                    </div>
                    <label class="toggle-switch">
                        <input type="checkbox" checked>
                        <span class="slider"></span>
                    </label>
                </div>
                
                <div class="setting-item">
                    <div class="setting-info">
                        <h4>Profile Visibility</h4>
                        <p>Allow other students to view your profile</p>
                    </div>
                    <label class="toggle-switch">
                        <input type="checkbox" checked>
                        <span class="slider"></span>
                    </label>
                </div>
                
                <div class="setting-item">
                    <div class="setting-info">
                        <h4>Show on Leaderboard</h4>
                        <p>Display your ranking on public leaderboards</p>
                    </div>
                    <label class="toggle-switch">
                        <input type="checkbox" checked>
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.edit-profile-page {
    max-width: 800px;
    margin: 0 auto;
}

.page-header {
    margin-bottom: 40px;
}

.breadcrumb {
    margin-bottom: 15px;
    color: #cccccc;
    font-size: 0.9rem;
}

.breadcrumb a {
    color: #FFD700;
    text-decoration: none;
}

.breadcrumb i {
    margin: 0 10px;
    font-size: 0.8rem;
}

.page-header h1 {
    color: #FFD700;
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.page-header p {
    color: #cccccc;
    font-size: 1.1rem;
}

.edit-content {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.edit-section {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px;
}

.section-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.section-header h2 {
    color: #FFD700;
    font-size: 1.5rem;
    margin-bottom: 8px;
}

.section-header p {
    color: #cccccc;
    font-size: 0.9rem;
}

.profile-form,
.password-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    color: #ffffff;
    font-weight: 500;
    font-size: 0.9rem;
}

.form-group input,
.form-group textarea {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 12px 16px;
    color: #ffffff;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #FFD700;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.avatar-upload {
    margin-bottom: 10px;
}

.avatar-section {
    display: flex;
    align-items: center;
    gap: 25px;
}

.current-avatar {
    flex-shrink: 0;
}

.current-avatar img,
.current-avatar .default-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 3px solid #FFD700;
}

.current-avatar .default-avatar {
    background: linear-gradient(135deg, #666 0%, #888 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 2rem;
}

.avatar-controls {
    flex: 1;
}

.btn-upload {
    background: rgba(255, 215, 0, 0.2);
    color: #FFD700;
    border: 1px solid #FFD700;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    margin-bottom: 8px;
}

.btn-upload:hover {
    background: #FFD700;
    color: #000000;
}

.upload-hint {
    color: #999;
    font-size: 0.8rem;
    margin: 0;
}

.error-message {
    color: #ff4444;
    font-size: 0.8rem;
    margin-top: 5px;
}

.form-actions {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-top: 10px;
}

.btn-save {
    background: #FFD700;
    color: #000000;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-save:hover {
    background: #FFA500;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

.btn-cancel {
    color: #cccccc;
    text-decoration: none;
    padding: 12px 25px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-cancel:hover {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
}

.settings-grid {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.setting-info h4 {
    color: #ffffff;
    margin-bottom: 5px;
    font-size: 1rem;
}

.setting-info p {
    color: #cccccc;
    font-size: 0.9rem;
    margin: 0;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.2);
    transition: 0.3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #FFD700;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .avatar-section {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .btn-save,
    .btn-cancel {
        text-align: center;
        justify-content: center;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Avatar preview
    const avatarInput = document.getElementById('avatar');
    const avatarPreview = document.getElementById('avatarPreview');
    
    avatarInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                if (avatarPreview.tagName === 'IMG') {
                    avatarPreview.src = e.target.result;
                } else {
                    // Replace default avatar with image
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.alt = 'Avatar Preview';
                    img.style.width = '80px';
                    img.style.height = '80px';
                    img.style.borderRadius = '50%';
                    img.style.border = '3px solid #FFD700';
                    avatarPreview.parentNode.replaceChild(img, avatarPreview);
                }
            };
            reader.readAsDataURL(file);
        }
    });
    
    // Form validation
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                
                // Re-enable after 3 seconds in case of error
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = submitBtn.innerHTML.replace('Saving...', submitBtn.textContent.includes('Password') ? 'Update Password' : 'Save Changes');
                }, 3000);
            }
        });
    });
});
</script>
@endsection
