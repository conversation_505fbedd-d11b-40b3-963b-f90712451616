<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Campus;

class CampusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $campuses = [
            [
                'name' => 'Ecommerce Campus',
                'slug' => 'ecommerce',
                'description' => 'Learn to build and scale profitable online stores. Master dropshipping, product research, advertising, and customer acquisition strategies.',
                'icon' => 'fas fa-store',
                'color' => '#4CAF50',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Copywriting Campus',
                'slug' => 'copywriting',
                'description' => 'Master the art of persuasive writing and sales. Learn to write compelling copy that converts prospects into customers.',
                'icon' => 'fas fa-pen',
                'color' => '#2196F3',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Crypto Campus',
                'slug' => 'crypto',
                'description' => 'Navigate the cryptocurrency markets like a pro. Learn trading strategies, DeFi, NFTs, and blockchain technology.',
                'icon' => 'fab fa-bitcoin',
                'color' => '#FF9800',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Stocks Campus',
                'slug' => 'stocks',
                'description' => 'Learn advanced trading strategies and market analysis. Master technical analysis, options trading, and portfolio management.',
                'icon' => 'fas fa-chart-line',
                'color' => '#9C27B0',
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Amazon FBA Campus',
                'slug' => 'amazon-fba',
                'description' => 'Build a successful Amazon business from scratch. Learn product sourcing, listing optimization, and scaling strategies.',
                'icon' => 'fab fa-amazon',
                'color' => '#FF5722',
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'name' => 'Freelancing Campus',
                'slug' => 'freelancing',
                'description' => 'Start and grow your freelancing business. Learn client acquisition, project management, and scaling your services.',
                'icon' => 'fas fa-laptop',
                'color' => '#607D8B',
                'is_active' => true,
                'sort_order' => 6,
            ],
            [
                'name' => 'Content Creation Campus',
                'slug' => 'content-creation',
                'description' => 'Build your personal brand and monetize your content. Learn video editing, social media growth, and audience building.',
                'icon' => 'fas fa-video',
                'color' => '#E91E63',
                'is_active' => true,
                'sort_order' => 7,
            ],
            [
                'name' => 'Real Estate Campus',
                'slug' => 'real-estate',
                'description' => 'Learn real estate investing and property management. Master wholesaling, flipping, and rental property strategies.',
                'icon' => 'fas fa-home',
                'color' => '#795548',
                'is_active' => true,
                'sort_order' => 8,
            ],
        ];

        foreach ($campuses as $campus) {
            Campus::create($campus);
        }
    }
}
