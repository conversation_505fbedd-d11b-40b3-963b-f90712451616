@extends('layouts.dashboard')

@section('title', $lesson->title)

@section('content')
<div class="lesson-page">
    <!-- <PERSON>on <PERSON>er -->
    <div class="lesson-header">
        <div class="breadcrumb">
            <a href="{{ route('campuses.show', $campus) }}">{{ $campus->name }}</a>
            <i class="fas fa-chevron-right"></i>
            <a href="{{ route('courses.show', [$campus, $course]) }}">{{ $course->title }}</a>
            <i class="fas fa-chevron-right"></i>
            <span>{{ $lesson->title }}</span>
        </div>
        
        <div class="lesson-info">
            <h1>{{ $lesson->title }}</h1>
            @if($lesson->description)
                <p>{{ $lesson->description }}</p>
            @endif
            
            <div class="lesson-meta">
                @if($lesson->video_duration)
                    <div class="meta-item">
                        <i class="fas fa-clock"></i>
                        <span>{{ $lesson->video_duration }}</span>
                    </div>
                @endif
                @if($lesson->is_free)
                    <div class="meta-item">
                        <i class="fas fa-unlock"></i>
                        <span>Free Lesson</span>
                    </div>
                @endif
                <div class="meta-item">
                    <i class="fas fa-chart-line"></i>
                    <span>{{ $userProgress->progress_percentage }}% Complete</span>
                </div>
            </div>
        </div>
        
        <div class="lesson-navigation">
            @if($previousLesson)
                <a href="{{ route('lessons.show', [$campus, $course, $previousLesson]) }}" class="nav-btn prev">
                    <i class="fas fa-chevron-left"></i>
                    Previous
                </a>
            @endif
            
            @if($nextLesson)
                <a href="{{ route('lessons.show', [$campus, $course, $nextLesson]) }}" class="nav-btn next">
                    Next
                    <i class="fas fa-chevron-right"></i>
                </a>
            @endif
        </div>
    </div>

    <!-- Lesson Content -->
    <div class="lesson-content">
        <div class="main-content">
            <!-- Video Player -->
            @if($lesson->video_url)
                <div class="video-container">
                    <div class="video-player" id="videoPlayer">
                        <video controls width="100%" height="400" id="lessonVideo">
                            <source src="{{ $lesson->video_url }}" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                    </div>
                    
                    <div class="video-controls">
                        <button id="playPauseBtn" class="control-btn">
                            <i class="fas fa-play"></i>
                        </button>
                        <div class="progress-container">
                            <div class="progress-bar" id="progressBar">
                                <div class="progress-fill" id="progressFill"></div>
                            </div>
                            <span class="time-display" id="timeDisplay">0:00 / 0:00</span>
                        </div>
                        <button id="fullscreenBtn" class="control-btn">
                            <i class="fas fa-expand"></i>
                        </button>
                    </div>
                </div>
            @endif

            <!-- Lesson Text Content -->
            @if($lesson->content)
                <div class="text-content">
                    <h2>Lesson Content</h2>
                    <div class="content-body">
                        {!! nl2br(e($lesson->content)) !!}
                    </div>
                </div>
            @endif

            <!-- Attachments -->
            @if($lesson->attachments)
                <div class="attachments-section">
                    <h3>Downloads & Resources</h3>
                    <div class="attachments-list">
                        @foreach($lesson->attachments as $attachment)
                            <div class="attachment-item">
                                <i class="fas fa-file-download"></i>
                                <span>{{ $attachment['name'] ?? 'Download' }}</span>
                                <a href="{{ $attachment['url'] }}" class="download-btn" download>
                                    <i class="fas fa-download"></i>
                                </a>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Lesson Actions -->
            <div class="lesson-actions">
                @if(!$userProgress->is_completed)
                    <button id="markCompleteBtn" class="btn-complete">
                        <i class="fas fa-check"></i>
                        Mark as Complete
                    </button>
                @else
                    <div class="completion-badge">
                        <i class="fas fa-check-circle"></i>
                        Lesson Completed
                    </div>
                @endif
                
                @if($nextLesson)
                    <a href="{{ route('lessons.show', [$campus, $course, $nextLesson]) }}" class="btn-next">
                        Next Lesson
                        <i class="fas fa-arrow-right"></i>
                    </a>
                @else
                    <a href="{{ route('courses.show', [$campus, $course]) }}" class="btn-next">
                        Back to Course
                        <i class="fas fa-arrow-left"></i>
                    </a>
                @endif
            </div>
        </div>

        <!-- Lesson Sidebar -->
        <div class="lesson-sidebar">
            <div class="progress-card">
                <h3>Your Progress</h3>
                <div class="circular-progress" data-progress="{{ $userProgress->progress_percentage }}">
                    <span>{{ $userProgress->progress_percentage }}%</span>
                </div>
                <p>{{ $userProgress->is_completed ? 'Completed' : 'In Progress' }}</p>
            </div>

            <div class="course-outline">
                <h3>Course Outline</h3>
                <div class="lessons-list">
                    @foreach($course->lessons()->published()->orderBy('sort_order')->get() as $courseLesson)
                        @php
                            $lessonProgress = Auth::user()->progress()->where('lesson_id', $courseLesson->id)->first();
                            $isCurrentLesson = $courseLesson->id === $lesson->id;
                        @endphp
                        
                        <div class="outline-lesson {{ $isCurrentLesson ? 'current' : '' }} {{ $lessonProgress && $lessonProgress->is_completed ? 'completed' : '' }}">
                            <div class="lesson-status">
                                @if($lessonProgress && $lessonProgress->is_completed)
                                    <i class="fas fa-check-circle"></i>
                                @elseif($isCurrentLesson)
                                    <i class="fas fa-play-circle"></i>
                                @else
                                    <i class="far fa-circle"></i>
                                @endif
                            </div>
                            <div class="lesson-details">
                                <a href="{{ route('lessons.show', [$campus, $course, $courseLesson]) }}">
                                    {{ $courseLesson->title }}
                                </a>
                                @if($courseLesson->video_duration)
                                    <span class="duration">{{ $courseLesson->video_duration }}</span>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>

            <div class="help-card">
                <h3>Need Help?</h3>
                <p>Stuck on something? Join the campus chat room to get help from professors and fellow students.</p>
                <a href="#" class="btn-help">
                    <i class="fas fa-comments"></i>
                    Join Chat Room
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.lesson-page {
    max-width: 1400px;
    margin: 0 auto;
}

.lesson-header {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.breadcrumb {
    margin-bottom: 20px;
    color: #cccccc;
    font-size: 0.9rem;
}

.breadcrumb a {
    color: #FFD700;
    text-decoration: none;
}

.breadcrumb i {
    margin: 0 10px;
    font-size: 0.8rem;
}

.lesson-info {
    margin-bottom: 20px;
}

.lesson-info h1 {
    color: #FFD700;
    font-size: 2rem;
    margin-bottom: 10px;
}

.lesson-info p {
    color: #cccccc;
    margin-bottom: 15px;
    line-height: 1.6;
}

.lesson-meta {
    display: flex;
    gap: 25px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #999;
    font-size: 0.9rem;
}

.meta-item i {
    color: #FFD700;
}

.lesson-navigation {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.nav-btn {
    padding: 10px 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    color: #ffffff;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background: rgba(255, 215, 0, 0.2);
    border-color: #FFD700;
    color: #FFD700;
}

.lesson-content {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 30px;
}

.main-content {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.video-container {
    margin-bottom: 30px;
}

.video-player {
    background: #000;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
}

.video-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.8);
}

.control-btn {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.progress-container {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 15px;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    cursor: pointer;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: #FFD700;
    border-radius: 3px;
    transition: width 0.1s ease;
}

.time-display {
    color: #ffffff;
    font-size: 0.9rem;
    min-width: 80px;
}

.text-content {
    margin-bottom: 30px;
}

.text-content h2 {
    color: #FFD700;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.content-body {
    color: #cccccc;
    line-height: 1.8;
    font-size: 1rem;
}

.attachments-section {
    margin-bottom: 30px;
}

.attachments-section h3 {
    color: #FFD700;
    margin-bottom: 15px;
}

.attachments-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.attachment-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.attachment-item i:first-child {
    color: #FFD700;
    font-size: 1.2rem;
}

.attachment-item span {
    flex: 1;
    color: #ffffff;
}

.download-btn {
    color: #FFD700;
    text-decoration: none;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.download-btn:hover {
    background: rgba(255, 215, 0, 0.2);
}

.lesson-actions {
    display: flex;
    gap: 15px;
    align-items: center;
}

.btn-complete {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-complete:hover {
    background: #45a049;
    transform: translateY(-2px);
}

.completion-badge {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 8px;
    border: 1px solid #4CAF50;
}

.btn-next {
    background: #FFD700;
    color: #000000;
    padding: 12px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-next:hover {
    background: #FFA500;
    transform: translateY(-2px);
}

.lesson-sidebar {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.progress-card,
.course-outline,
.help-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
}

.progress-card h3,
.course-outline h3,
.help-card h3 {
    color: #FFD700;
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.circular-progress {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: conic-gradient(#FFD700 calc(var(--progress) * 3.6deg), rgba(255,255,255,0.1) 0deg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    position: relative;
}

.circular-progress::before {
    content: '';
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #1a1a1a;
    position: absolute;
}

.circular-progress span {
    color: #ffffff;
    font-weight: bold;
    z-index: 1;
}

.progress-card p {
    text-align: center;
    color: #cccccc;
}

.lessons-list {
    max-height: 400px;
    overflow-y: auto;
}

.outline-lesson {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.outline-lesson:last-child {
    border-bottom: none;
}

.outline-lesson.current {
    background: rgba(255, 215, 0, 0.1);
    border-radius: 8px;
    padding: 10px;
    margin: 0 -10px;
}

.lesson-status i {
    color: #666;
}

.outline-lesson.completed .lesson-status i {
    color: #4CAF50;
}

.outline-lesson.current .lesson-status i {
    color: #FFD700;
}

.lesson-details {
    flex: 1;
}

.lesson-details a {
    color: #ffffff;
    text-decoration: none;
    font-size: 0.9rem;
}

.lesson-details a:hover {
    color: #FFD700;
}

.duration {
    color: #999;
    font-size: 0.8rem;
    display: block;
    margin-top: 2px;
}

.help-card p {
    color: #cccccc;
    margin-bottom: 15px;
    line-height: 1.5;
}

.btn-help {
    width: 100%;
    background: rgba(255, 215, 0, 0.2);
    color: #FFD700;
    border: 1px solid #FFD700;
    padding: 12px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-help:hover {
    background: #FFD700;
    color: #000000;
}

@media (max-width: 768px) {
    .lesson-content {
        grid-template-columns: 1fr;
    }
    
    .lesson-sidebar {
        order: -1;
    }
    
    .lesson-navigation {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .lesson-actions {
        flex-direction: column;
    }
    
    .btn-complete,
    .btn-next {
        width: 100%;
        justify-content: center;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const video = document.getElementById('lessonVideo');
    const playPauseBtn = document.getElementById('playPauseBtn');
    const progressBar = document.getElementById('progressBar');
    const progressFill = document.getElementById('progressFill');
    const timeDisplay = document.getElementById('timeDisplay');
    const fullscreenBtn = document.getElementById('fullscreenBtn');
    const markCompleteBtn = document.getElementById('markCompleteBtn');
    
    let startTime = Date.now();
    let totalWatchTime = 0;
    
    if (video) {
        // Play/Pause functionality
        playPauseBtn?.addEventListener('click', function() {
            if (video.paused) {
                video.play();
                this.innerHTML = '<i class="fas fa-pause"></i>';
            } else {
                video.pause();
                this.innerHTML = '<i class="fas fa-play"></i>';
            }
        });
        
        // Progress bar functionality
        video.addEventListener('timeupdate', function() {
            const progress = (video.currentTime / video.duration) * 100;
            progressFill.style.width = progress + '%';
            
            const currentMinutes = Math.floor(video.currentTime / 60);
            const currentSeconds = Math.floor(video.currentTime % 60);
            const durationMinutes = Math.floor(video.duration / 60);
            const durationSeconds = Math.floor(video.duration % 60);
            
            timeDisplay.textContent = `${currentMinutes}:${currentSeconds.toString().padStart(2, '0')} / ${durationMinutes}:${durationSeconds.toString().padStart(2, '0')}`;
            
            // Update lesson progress
            updateLessonProgress(progress);
        });
        
        // Progress bar click
        progressBar?.addEventListener('click', function(e) {
            const rect = this.getBoundingClientRect();
            const clickX = e.clientX - rect.left;
            const width = rect.width;
            const percentage = (clickX / width) * 100;
            const newTime = (percentage / 100) * video.duration;
            video.currentTime = newTime;
        });
        
        // Fullscreen functionality
        fullscreenBtn?.addEventListener('click', function() {
            if (video.requestFullscreen) {
                video.requestFullscreen();
            }
        });
        
        // Track watch time
        video.addEventListener('play', function() {
            startTime = Date.now();
        });
        
        video.addEventListener('pause', function() {
            totalWatchTime += Date.now() - startTime;
        });
    }
    
    // Mark complete functionality
    markCompleteBtn?.addEventListener('click', function() {
        updateLessonProgress(100);
    });
    
    function updateLessonProgress(progressPercentage) {
        const currentTime = Date.now();
        const sessionTime = Math.floor((currentTime - startTime) / 1000);
        
        fetch(`{{ route('lessons.update-progress', [$campus, $course, $lesson]) }}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                progress_percentage: Math.floor(progressPercentage),
                time_spent: totalWatchTime + sessionTime
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.progress.is_completed && markCompleteBtn) {
                markCompleteBtn.style.display = 'none';
                const completionBadge = document.createElement('div');
                completionBadge.className = 'completion-badge';
                completionBadge.innerHTML = '<i class="fas fa-check-circle"></i> Lesson Completed';
                markCompleteBtn.parentNode.insertBefore(completionBadge, markCompleteBtn);
                
                // Show notification
                if (window.showNotification) {
                    window.showNotification(data.message, 'success');
                }
            }
        })
        .catch(error => console.error('Error updating progress:', error));
    }
    
    // Update circular progress
    const circularProgress = document.querySelector('.circular-progress');
    if (circularProgress) {
        const progress = circularProgress.getAttribute('data-progress');
        circularProgress.style.setProperty('--progress', progress + '%');
    }
});
</script>
@endsection
