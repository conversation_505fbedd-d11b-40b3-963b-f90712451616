<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Campus;
use App\Models\ChatRoom;
use App\Models\Message;
use Illuminate\Support\Facades\Auth;

class ChatController extends Controller
{
    /**
     * Display the specified chat room.
     */
    public function show(Campus $campus, ChatRoom $chatRoom)
    {
        // Check if user is enrolled in the campus
        if (!Auth::user()->campuses()->where('campus_id', $campus->id)->exists()) {
            return redirect()->route('campuses.enroll', $campus)->with('error', 'You need to enroll in this campus first.');
        }

        // Check if chat room belongs to campus
        if ($chatRoom->campus_id !== $campus->id) {
            abort(404);
        }

        // Get recent messages (last 50)
        $messages = $chatRoom->messages()
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->limit(50)
            ->get()
            ->reverse()
            ->values();

        // Get online users count (simulated)
        $onlineCount = rand(20, 150);

        return view('chat.show', compact('campus', 'chatRoom', 'messages', 'onlineCount'));
    }

    /**
     * Send a message to the chat room.
     */
    public function sendMessage(Request $request, Campus $campus, ChatRoom $chatRoom)
    {
        // Check if user is enrolled in the campus
        if (!Auth::user()->campuses()->where('campus_id', $campus->id)->exists()) {
            return response()->json(['error' => 'Not enrolled in campus'], 403);
        }

        // Check if chat room belongs to campus
        if ($chatRoom->campus_id !== $campus->id) {
            return response()->json(['error' => 'Invalid chat room'], 404);
        }

        $request->validate([
            'content' => 'required|string|max:1000',
        ]);

        $message = Message::create([
            'chat_room_id' => $chatRoom->id,
            'user_id' => Auth::id(),
            'content' => $request->content,
        ]);

        $message->load('user');

        return response()->json([
            'success' => true,
            'message' => [
                'id' => $message->id,
                'content' => $message->content,
                'user' => [
                    'id' => $message->user->id,
                    'name' => $message->user->name,
                    'username' => $message->user->username,
                    'avatar' => $message->user->avatar,
                    'role' => $message->user->role,
                ],
                'created_at' => $message->created_at->format('H:i'),
                'is_own' => true,
            ]
        ]);
    }

    /**
     * Get recent messages for a chat room.
     */
    public function getMessages(Campus $campus, ChatRoom $chatRoom)
    {
        // Check if user is enrolled in the campus
        if (!Auth::user()->campuses()->where('campus_id', $campus->id)->exists()) {
            return response()->json(['error' => 'Not enrolled in campus'], 403);
        }

        // Check if chat room belongs to campus
        if ($chatRoom->campus_id !== $campus->id) {
            return response()->json(['error' => 'Invalid chat room'], 404);
        }

        $messages = $chatRoom->messages()
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get()
            ->reverse()
            ->values()
            ->map(function ($message) {
                return [
                    'id' => $message->id,
                    'content' => $message->content,
                    'user' => [
                        'id' => $message->user->id,
                        'name' => $message->user->name,
                        'username' => $message->user->username,
                        'avatar' => $message->user->avatar,
                        'role' => $message->user->role,
                    ],
                    'created_at' => $message->created_at->format('H:i'),
                    'is_own' => $message->user_id === Auth::id(),
                ];
            });

        return response()->json([
            'success' => true,
            'messages' => $messages,
        ]);
    }
}
