/* The Real World - Responsive Enhancements */

/* Mobile-First Responsive Design */
@media (max-width: 480px) {
    /* Typography Adjustments */
    .hero-title {
        font-size: 2rem !important;
        letter-spacing: 1px;
    }
    
    .hero-subtitle {
        font-size: 1rem !important;
    }
    
    .section-title {
        font-size: 1.8rem !important;
    }
    
    /* Spacing Adjustments */
    .container {
        padding: 0 15px;
    }
    
    .hero-container {
        padding: 0 15px;
        gap: 30px;
    }
    
    /* Button Adjustments */
    .btn-primary,
    .btn-secondary {
        padding: 12px 20px;
        font-size: 0.9rem;
        width: 100%;
        text-align: center;
    }
    
    .hero-buttons {
        flex-direction: column;
        gap: 12px;
        width: 100%;
    }
    
    /* Card Adjustments */
    .feature-card,
    .campus-card {
        padding: 20px;
        margin-bottom: 15px;
    }
    
    .feature-card h3,
    .campus-card h3 {
        font-size: 1.1rem;
    }
    
    /* Stats Adjustments */
    .hero-stats {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .stat {
        min-width: auto;
    }
    
    /* Navigation Mobile */
    .navbar {
        padding: 10px 0;
    }
    
    .nav-container {
        padding: 0 15px;
    }
    
    .nav-logo h2 {
        font-size: 1.2rem;
    }
    
    .nav-links {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        background: rgba(0, 0, 0, 0.95);
        backdrop-filter: blur(10px);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 30px;
        z-index: 10000;
    }
    
    .nav-links.active {
        display: flex;
    }
    
    .nav-links a {
        font-size: 1.5rem;
        color: #ffffff;
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    .nav-links a:hover {
        color: #FFD700;
        transform: scale(1.1);
    }
    
    .mobile-menu-toggle {
        display: block;
        background: none;
        border: none;
        color: #FFD700;
        font-size: 1.5rem;
        cursor: pointer;
        z-index: 10001;
        position: relative;
    }
    
    .mobile-menu-close {
        position: absolute;
        top: 30px;
        right: 30px;
        background: none;
        border: none;
        color: #FFD700;
        font-size: 2rem;
        cursor: pointer;
    }
}

@media (min-width: 481px) {
    .mobile-menu-toggle {
        display: none;
    }
}

/* Tablet Responsive */
@media (max-width: 768px) {
    /* Grid Adjustments */
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    .campuses-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    /* Hero Adjustments */
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 40px;
    }
    
    .hero-image {
        order: -1;
    }
    
    /* Dashboard Responsive */
    .dashboard-layout {
        grid-template-columns: 1fr;
    }
    
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        z-index: 10000;
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        padding: 20px 15px;
    }
    
    .mobile-sidebar-toggle {
        display: block;
        position: fixed;
        top: 20px;
        left: 20px;
        z-index: 10001;
        background: rgba(255, 215, 0, 0.9);
        color: #000000;
        border: none;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
    
    .sidebar-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 9999;
    }
    
    .sidebar-overlay.active {
        display: block;
    }
    
    /* Card Grid Responsive */
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .welcome-stats {
        flex-direction: column;
        gap: 15px;
    }
    
    .stat-card {
        text-align: center;
    }
}

@media (min-width: 769px) {
    .mobile-sidebar-toggle {
        display: none;
    }
}

/* Large Tablet / Small Desktop */
@media (max-width: 1024px) {
    .hero-container {
        gap: 50px;
    }
    
    .hero-title {
        font-size: 3rem;
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .campuses-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Ultra-wide Screen Optimizations */
@media (min-width: 1400px) {
    .container {
        max-width: 1400px;
    }
    
    .hero-container {
        max-width: 1400px;
    }
    
    .features-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .campuses-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* High DPI / Retina Display Optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-title {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    
    .btn-primary,
    .btn-secondary {
        -webkit-font-smoothing: antialiased;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #ffffff;
        --text-secondary: #cccccc;
        --bg-primary: #0a0a0a;
        --bg-secondary: #1a1a1a;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .parallax {
        transform: none !important;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .sidebar,
    .hero-buttons,
    .btn-primary,
    .btn-secondary {
        display: none !important;
    }
    
    .hero-title,
    .section-title {
        color: #000000 !important;
    }
    
    .container {
        max-width: none !important;
        padding: 0 !important;
    }
    
    .feature-card,
    .campus-card {
        break-inside: avoid;
        border: 1px solid #cccccc;
        margin-bottom: 20px;
    }
}

/* Focus and Accessibility Improvements */
.btn-primary:focus,
.btn-secondary:focus,
.nav-links a:focus {
    outline: 2px solid #FFD700;
    outline-offset: 2px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 25%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Smooth Scrolling Enhancement */
html {
    scroll-behavior: smooth;
}

/* Custom Scrollbar for Webkit Browsers */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 6px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--primary-gold), var(--primary-gold-dark));
    border-radius: 6px;
    border: 2px solid var(--bg-secondary);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, var(--primary-gold-light), var(--primary-gold));
}

/* Enhanced Touch Targets for Mobile */
@media (max-width: 768px) {
    .btn-primary,
    .btn-secondary,
    .nav-links a {
        min-height: 44px;
        min-width: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .feature-card,
    .campus-card {
        min-height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
}

/* Performance Optimizations */
.hero::before,
.hero::after {
    will-change: transform;
}

.card {
    will-change: transform;
}

.nav-item a::before {
    will-change: left;
}

/* Container Queries Support (Future-proofing) */
@supports (container-type: inline-size) {
    .feature-card {
        container-type: inline-size;
    }
    
    @container (max-width: 300px) {
        .feature-card h3 {
            font-size: 1rem;
        }
        
        .feature-card p {
            font-size: 0.8rem;
        }
    }
}
