/* The Real World - Global Styles */

/* CSS Variables for consistent theming */
:root {
    /* Primary Colors */
    --primary-gold: #FFD700;
    --primary-gold-dark: #FFA500;
    --primary-gold-light: #FFED4E;
    
    /* Background Colors */
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #2a2a2a;
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #999999;
    --text-dark: #666666;
    
    /* Status Colors */
    --success: #4CAF50;
    --success-bg: rgba(76, 175, 80, 0.1);
    --warning: #FF9800;
    --warning-bg: rgba(255, 152, 0, 0.1);
    --error: #f44336;
    --error-bg: rgba(244, 67, 54, 0.1);
    --info: #2196F3;
    --info-bg: rgba(33, 150, 243, 0.1);
    
    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;
    
    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    --radius-full: 50%;
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.25);
    --shadow-gold: 0 0 20px rgba(255, 215, 0, 0.3);
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Typography */
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Typography Enhancements */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

a {
    color: var(--primary-gold);
    text-decoration: none;
    transition: var(--transition-normal);
}

a:hover {
    color: var(--primary-gold-light);
    text-decoration: underline;
}

/* Enhanced Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-dark) 100%);
    color: var(--bg-primary);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), var(--shadow-gold);
    color: var(--bg-primary);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: var(--primary-gold);
    color: var(--text-primary);
}

.btn-outline {
    background: transparent;
    color: var(--primary-gold);
    border: 2px solid var(--primary-gold);
}

.btn-outline:hover {
    background: var(--primary-gold);
    color: var(--bg-primary);
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, var(--success) 0%, #45a049 100%);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning) 0%, #f57c00 100%);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, var(--error) 0%, #d32f2f 100%);
    color: white;
}

.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
}

.btn-lg {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: var(--font-size-lg);
}

/* Enhanced Card Styles */
.card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-gold), var(--primary-gold-dark));
    transform: scaleX(0);
    transition: var(--transition-normal);
}

.card:hover {
    transform: translateY(-5px);
    border-color: rgba(255, 215, 0, 0.3);
    box-shadow: var(--shadow-xl);
}

.card:hover::before {
    transform: scaleX(1);
}

.card-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.card-title {
    color: var(--primary-gold);
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-sm);
}

.card-subtitle {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Enhanced Form Styles */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.form-control {
    width: 100%;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    transition: var(--transition-normal);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-gold);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

.form-control::placeholder {
    color: var(--text-muted);
}

/* Enhanced Badge Styles */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary { background: var(--primary-gold); color: var(--bg-primary); }
.badge-success { background: var(--success-bg); color: var(--success); border: 1px solid var(--success); }
.badge-warning { background: var(--warning-bg); color: var(--warning); border: 1px solid var(--warning); }
.badge-danger { background: var(--error-bg); color: var(--error); border: 1px solid var(--error); }
.badge-info { background: var(--info-bg); color: var(--info); border: 1px solid var(--info); }

/* Enhanced Progress Bars */
.progress {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-full);
    overflow: hidden;
    position: relative;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-gold), var(--primary-gold-dark));
    border-radius: var(--radius-full);
    transition: width var(--transition-slow);
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Enhanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px var(--primary-gold); }
    50% { box-shadow: 0 0 20px var(--primary-gold), 0 0 30px var(--primary-gold); }
}

/* Animation Classes */
.animate-fade-in-up { animation: fadeInUp 0.6s ease-out; }
.animate-fade-in-left { animation: fadeInLeft 0.6s ease-out; }
.animate-fade-in-right { animation: fadeInRight 0.6s ease-out; }
.animate-pulse { animation: pulse 2s infinite; }
.animate-bounce { animation: bounce 2s infinite; }
.animate-glow { animation: glow 2s infinite; }

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--primary-gold); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-success { color: var(--success); }
.text-warning { color: var(--warning); }
.text-danger { color: var(--error); }

.bg-primary { background: var(--bg-primary); }
.bg-secondary { background: var(--bg-secondary); }
.bg-tertiary { background: var(--bg-tertiary); }

.d-flex { display: flex; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-none { display: none; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

.align-center { align-items: center; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }

.flex-1 { flex: 1; }
.flex-wrap { flex-wrap: wrap; }
.flex-col { flex-direction: column; }

.gap-xs { gap: var(--spacing-xs); }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }
.gap-xl { gap: var(--spacing-xl); }

.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }
.mr-0 { margin-right: 0; }

.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }
.pr-0 { padding-right: 0; }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

.transition { transition: var(--transition-normal); }
.transition-fast { transition: var(--transition-fast); }
.transition-slow { transition: var(--transition-slow); }

/* Responsive Utilities */
@media (max-width: 768px) {
    .mobile-hidden { display: none; }
    .mobile-block { display: block; }
    .mobile-flex { display: flex; }
    .mobile-text-center { text-align: center; }
    .mobile-text-left { text-align: left; }
}

@media (min-width: 769px) {
    .desktop-hidden { display: none; }
    .desktop-block { display: block; }
    .desktop-flex { display: flex; }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gold);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-gold-dark);
}

/* Selection Styling */
::selection {
    background: var(--primary-gold);
    color: var(--bg-primary);
}

::-moz-selection {
    background: var(--primary-gold);
    color: var(--bg-primary);
}
