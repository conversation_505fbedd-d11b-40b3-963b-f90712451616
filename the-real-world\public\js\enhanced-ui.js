/**
 * The Real World - Enhanced UI Interactions
 * Advanced JavaScript for smooth animations and user experience
 */

class EnhancedUI {
    constructor() {
        this.init();
    }

    init() {
        this.setupScrollAnimations();
        this.setupParallaxEffects();
        this.setupSmoothScrolling();
        this.setupLoadingStates();
        this.setupNotifications();
        this.setupProgressAnimations();
        this.setupCardInteractions();
        this.setupFormEnhancements();
        this.setupTypingEffects();
        this.setupCounterAnimations();
    }

    // Scroll-triggered animations
    setupScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                    
                    // Stagger child animations
                    const children = entry.target.querySelectorAll('.stagger-child');
                    children.forEach((child, index) => {
                        setTimeout(() => {
                            child.classList.add('animate-in');
                        }, index * 100);
                    });
                }
            });
        }, observerOptions);

        // Observe elements with animation classes
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });
    }

    // Parallax scrolling effects
    setupParallaxEffects() {
        let ticking = false;

        const updateParallax = () => {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('.parallax');

            parallaxElements.forEach(element => {
                const speed = element.dataset.speed || 0.5;
                const yPos = -(scrolled * speed);
                element.style.transform = `translateY(${yPos}px)`;
            });

            ticking = false;
        };

        const requestTick = () => {
            if (!ticking) {
                requestAnimationFrame(updateParallax);
                ticking = true;
            }
        };

        window.addEventListener('scroll', requestTick);
    }

    // Smooth scrolling for anchor links
    setupSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    // Loading states for buttons and forms
    setupLoadingStates() {
        document.addEventListener('submit', (e) => {
            const form = e.target;
            const submitBtn = form.querySelector('button[type="submit"]');
            
            if (submitBtn && !submitBtn.disabled) {
                this.setLoadingState(submitBtn, true);
                
                // Reset after 3 seconds if no response
                setTimeout(() => {
                    this.setLoadingState(submitBtn, false);
                }, 3000);
            }
        });

        // Button click loading states
        document.querySelectorAll('.btn-loading').forEach(btn => {
            btn.addEventListener('click', () => {
                this.setLoadingState(btn, true);
                
                setTimeout(() => {
                    this.setLoadingState(btn, false);
                }, 2000);
            });
        });
    }

    setLoadingState(button, loading) {
        if (loading) {
            button.disabled = true;
            button.classList.add('loading');
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
        } else {
            button.disabled = false;
            button.classList.remove('loading');
            button.innerHTML = button.dataset.originalText || 'Submit';
        }
    }

    // Enhanced notification system
    setupNotifications() {
        window.showNotification = (message, type = 'info', duration = 5000) => {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                    <span>${message}</span>
                </div>
                <button class="notification-close">&times;</button>
            `;

            // Add to container or create one
            let container = document.querySelector('.notification-container');
            if (!container) {
                container = document.createElement('div');
                container.className = 'notification-container';
                document.body.appendChild(container);
            }

            container.appendChild(notification);

            // Animate in
            setTimeout(() => notification.classList.add('show'), 10);

            // Auto remove
            const removeNotification = () => {
                notification.classList.add('hide');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            };

            // Close button
            notification.querySelector('.notification-close').addEventListener('click', removeNotification);

            // Auto remove after duration
            setTimeout(removeNotification, duration);
        };
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    // Animated progress bars
    setupProgressAnimations() {
        const animateProgress = (progressBar) => {
            const targetWidth = progressBar.dataset.progress || '0';
            const fill = progressBar.querySelector('.progress-fill');
            
            if (fill) {
                fill.style.width = '0%';
                setTimeout(() => {
                    fill.style.width = targetWidth + '%';
                }, 100);
            }
        };

        // Animate progress bars when they come into view
        const progressObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateProgress(entry.target);
                    progressObserver.unobserve(entry.target);
                }
            });
        });

        document.querySelectorAll('.progress').forEach(progress => {
            progressObserver.observe(progress);
        });
    }

    // Enhanced card interactions
    setupCardInteractions() {
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-8px) scale(1.02)';
                card.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 215, 0, 0.2)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = '';
                card.style.boxShadow = '';
            });

            // Add ripple effect on click
            card.addEventListener('click', (e) => {
                const ripple = document.createElement('span');
                const rect = card.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(255, 215, 0, 0.3);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s ease-out;
                    pointer-events: none;
                `;

                card.style.position = 'relative';
                card.style.overflow = 'hidden';
                card.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    }

    // Form enhancements
    setupFormEnhancements() {
        // Floating labels
        document.querySelectorAll('.form-floating').forEach(container => {
            const input = container.querySelector('input, textarea');
            const label = container.querySelector('label');

            if (input && label) {
                const updateLabel = () => {
                    if (input.value || input === document.activeElement) {
                        label.classList.add('floating');
                    } else {
                        label.classList.remove('floating');
                    }
                };

                input.addEventListener('focus', updateLabel);
                input.addEventListener('blur', updateLabel);
                input.addEventListener('input', updateLabel);
                updateLabel(); // Initial state
            }
        });

        // Real-time validation feedback
        document.querySelectorAll('input[required], textarea[required]').forEach(input => {
            input.addEventListener('blur', () => {
                if (input.value.trim() === '') {
                    input.classList.add('error');
                } else {
                    input.classList.remove('error');
                }
            });

            input.addEventListener('input', () => {
                if (input.classList.contains('error') && input.value.trim() !== '') {
                    input.classList.remove('error');
                }
            });
        });
    }

    // Typing effect for text elements
    setupTypingEffects() {
        document.querySelectorAll('.typing-effect').forEach(element => {
            const text = element.textContent;
            element.textContent = '';
            
            let i = 0;
            const typeWriter = () => {
                if (i < text.length) {
                    element.textContent += text.charAt(i);
                    i++;
                    setTimeout(typeWriter, 50);
                }
            };

            // Start typing when element comes into view
            const typingObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        typeWriter();
                        typingObserver.unobserve(entry.target);
                    }
                });
            });

            typingObserver.observe(element);
        });
    }

    // Animated counters
    setupCounterAnimations() {
        const animateCounter = (element) => {
            const target = parseInt(element.dataset.count);
            const duration = 2000;
            const increment = target / (duration / 16);
            let current = 0;

            const updateCounter = () => {
                current += increment;
                if (current < target) {
                    element.textContent = Math.floor(current).toLocaleString();
                    requestAnimationFrame(updateCounter);
                } else {
                    element.textContent = target.toLocaleString();
                }
            };

            updateCounter();
        };

        const counterObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounter(entry.target);
                    counterObserver.unobserve(entry.target);
                }
            });
        });

        document.querySelectorAll('.counter').forEach(counter => {
            counterObserver.observe(counter);
        });
    }
}

// Initialize enhanced UI when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new EnhancedUI();
});

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    .notification-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .notification {
        background: rgba(26, 26, 26, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        padding: 15px;
        min-width: 300px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        transform: translateX(100%);
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .notification.show {
        transform: translateX(0);
    }

    .notification.hide {
        transform: translateX(100%);
        opacity: 0;
    }

    .notification-content {
        display: flex;
        align-items: center;
        gap: 10px;
        color: #ffffff;
    }

    .notification-success { border-left: 4px solid #4CAF50; }
    .notification-error { border-left: 4px solid #f44336; }
    .notification-warning { border-left: 4px solid #FF9800; }
    .notification-info { border-left: 4px solid #2196F3; }

    .notification-close {
        background: none;
        border: none;
        color: #cccccc;
        font-size: 1.2rem;
        cursor: pointer;
        padding: 0;
        margin-left: 10px;
    }

    @keyframes ripple {
        to {
            transform: scale(2);
            opacity: 0;
        }
    }

    .animate-in {
        animation: fadeInUp 0.6s ease-out;
    }

    .stagger-child {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.6s ease-out;
    }

    .stagger-child.animate-in {
        opacity: 1;
        transform: translateY(0);
    }

    .form-floating label {
        position: absolute;
        top: 50%;
        left: 15px;
        transform: translateY(-50%);
        color: #999;
        transition: all 0.3s ease;
        pointer-events: none;
        background: rgba(26, 26, 26, 0.8);
        padding: 0 5px;
    }

    .form-floating label.floating {
        top: 0;
        font-size: 0.8rem;
        color: #FFD700;
    }

    .form-control.error {
        border-color: #f44336;
        box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1);
    }
`;
document.head.appendChild(style);
