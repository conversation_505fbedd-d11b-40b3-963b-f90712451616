<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\CampusController;
use App\Http\Controllers\CourseController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\TaskController;
use App\Http\Controllers\LeaderboardController;
use App\Http\Controllers\ProfileController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Landing page
Route::get('/', function () {
    return view('landing');
})->name('home');

// Authentication routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);
});

Route::middleware('auth')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
    Route::get('/dashboard', function () {
        return view('dashboard.index');
    })->name('dashboard');

    // Campus routes
    Route::get('/campuses', [CampusController::class, 'index'])->name('campuses.index');
    Route::get('/campuses/{campus}', [CampusController::class, 'show'])->name('campuses.show');
    Route::get('/campuses/{campus}/enroll', [CampusController::class, 'enroll'])->name('campuses.enroll');
    Route::post('/campuses/{campus}/enroll', [CampusController::class, 'processEnrollment'])->name('campuses.process-enrollment');

    // Course routes
    Route::get('/campuses/{campus}/courses/{course}', [CourseController::class, 'show'])->name('courses.show');
    Route::get('/campuses/{campus}/courses/{course}/lessons/{lesson}', [CourseController::class, 'showLesson'])->name('lessons.show');
    Route::post('/campuses/{campus}/courses/{course}/lessons/{lesson}/progress', [CourseController::class, 'updateProgress'])->name('lessons.update-progress');

    // Chat routes
    Route::get('/campuses/{campus}/chat/{chatRoom}', [ChatController::class, 'show'])->name('chat.show');
    Route::post('/campuses/{campus}/chat/{chatRoom}/send', [ChatController::class, 'sendMessage'])->name('chat.send-message');
    Route::get('/campuses/{campus}/chat/{chatRoom}/messages', [ChatController::class, 'getMessages'])->name('chat.get-messages');

    // Task routes
    Route::get('/campuses/{campus}/tasks', [TaskController::class, 'index'])->name('tasks.index');
    Route::get('/campuses/{campus}/tasks/{task}', [TaskController::class, 'show'])->name('tasks.show');
    Route::post('/campuses/{campus}/tasks/{task}/submit', [TaskController::class, 'submit'])->name('tasks.submit');
    Route::get('/campuses/{campus}/tasks-submissions', [TaskController::class, 'submissions'])->name('tasks.submissions');
    Route::get('/campuses/{campus}/leaderboard', [TaskController::class, 'leaderboard'])->name('tasks.leaderboard');

    // Global Leaderboard routes
    Route::get('/leaderboard', [LeaderboardController::class, 'index'])->name('leaderboard.index');
    Route::get('/leaderboard/campus/{campus}', [LeaderboardController::class, 'campus'])->name('leaderboard.campus');
    Route::get('/api/leaderboard', [LeaderboardController::class, 'api'])->name('leaderboard.api');

    // Profile routes
    Route::get('/profile', [ProfileController::class, 'show'])->name('profile.show');
    Route::get('/profile/{user}', [ProfileController::class, 'show'])->name('profile.user');
    Route::get('/profile-edit', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::put('/profile/password', [ProfileController::class, 'updatePassword'])->name('profile.update-password');
});

// Password reset routes (placeholder)
Route::get('/forgot-password', function () {
    return view('auth.forgot-password');
})->name('password.request');
