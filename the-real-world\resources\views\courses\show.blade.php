@extends('layouts.dashboard')

@section('title', $course->title)

@section('content')
<div class="course-page">
    <!-- Course Header -->
    <div class="course-header">
        <div class="breadcrumb">
            <a href="{{ route('campuses.show', $campus) }}">{{ $campus->name }}</a>
            <i class="fas fa-chevron-right"></i>
            <span>{{ $course->title }}</span>
        </div>
        
        <div class="course-info">
            <div class="course-details">
                <h1>{{ $course->title }}</h1>
                <p>{{ $course->description }}</p>
                
                <div class="course-meta">
                    <div class="meta-item">
                        <i class="fas fa-clock"></i>
                        <span>{{ $course->duration_minutes }} minutes</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-signal"></i>
                        <span>{{ ucfirst($course->difficulty) }}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-play"></i>
                        <span>{{ $lessons->count() }} lessons</span>
                    </div>
                </div>
            </div>
            
            <div class="course-progress-card">
                <div class="progress-circle" style="--progress: {{ $progressPercentage }}%">
                    <span>{{ $progressPercentage }}%</span>
                </div>
                <div class="progress-info">
                    <h3>Your Progress</h3>
                    <p>{{ $userProgress->where('is_completed', true)->count() }} of {{ $lessons->count() }} lessons completed</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Content -->
    <div class="course-content">
        <div class="lessons-list">
            <h2>Course Lessons</h2>
            
            @foreach($lessons as $index => $lesson)
                @php
                    $lessonProgress = $userProgress->get($lesson->id);
                    $isCompleted = $lessonProgress && $lessonProgress->is_completed;
                    $progressPercent = $lessonProgress ? $lessonProgress->progress_percentage : 0;
                    $isLocked = !$lesson->is_free && $index > 0 && !$userProgress->get($lessons[$index-1]->id)?->is_completed;
                @endphp
                
                <div class="lesson-item {{ $isCompleted ? 'completed' : '' }} {{ $isLocked ? 'locked' : '' }}">
                    <div class="lesson-number">
                        @if($isCompleted)
                            <i class="fas fa-check"></i>
                        @elseif($isLocked)
                            <i class="fas fa-lock"></i>
                        @else
                            {{ $index + 1 }}
                        @endif
                    </div>
                    
                    <div class="lesson-content">
                        <div class="lesson-header">
                            <h3>{{ $lesson->title }}</h3>
                            <div class="lesson-badges">
                                @if($lesson->is_free)
                                    <span class="badge free">Free</span>
                                @endif
                                @if($lesson->video_duration)
                                    <span class="badge duration">{{ $lesson->video_duration }}</span>
                                @endif
                            </div>
                        </div>
                        
                        @if($lesson->description)
                            <p class="lesson-description">{{ $lesson->description }}</p>
                        @endif
                        
                        @if($progressPercent > 0 && !$isCompleted)
                            <div class="lesson-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: {{ $progressPercent }}%"></div>
                                </div>
                                <span class="progress-text">{{ $progressPercent }}% complete</span>
                            </div>
                        @endif
                    </div>
                    
                    <div class="lesson-actions">
                        @if($isLocked)
                            <button class="btn-lesson locked" disabled>
                                <i class="fas fa-lock"></i>
                                Locked
                            </button>
                        @else
                            <a href="{{ route('lessons.show', [$campus, $course, $lesson]) }}" class="btn-lesson">
                                @if($isCompleted)
                                    <i class="fas fa-redo"></i>
                                    Review
                                @elseif($progressPercent > 0)
                                    <i class="fas fa-play"></i>
                                    Continue
                                @else
                                    <i class="fas fa-play"></i>
                                    Start
                                @endif
                            </a>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
        
        <!-- Course Sidebar -->
        <div class="course-sidebar">
            <div class="sidebar-card">
                <h3>Course Overview</h3>
                <div class="overview-stats">
                    <div class="stat">
                        <span class="stat-number">{{ $lessons->count() }}</span>
                        <span class="stat-label">Total Lessons</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">{{ $userProgress->where('is_completed', true)->count() }}</span>
                        <span class="stat-label">Completed</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">{{ $course->duration_minutes }}m</span>
                        <span class="stat-label">Duration</span>
                    </div>
                </div>
            </div>
            
            <div class="sidebar-card">
                <h3>What You'll Learn</h3>
                <ul class="learning-objectives">
                    @switch($campus->slug)
                        @case('ecommerce')
                            <li>Product research techniques</li>
                            <li>Store optimization strategies</li>
                            <li>Marketing and advertising</li>
                            <li>Scaling your business</li>
                            @break
                        @case('copywriting')
                            <li>Psychology of persuasion</li>
                            <li>Email marketing mastery</li>
                            <li>Sales page optimization</li>
                            <li>Client acquisition</li>
                            @break
                        @case('crypto')
                            <li>Technical analysis</li>
                            <li>Risk management</li>
                            <li>Trading strategies</li>
                            <li>Portfolio management</li>
                            @break
                        @default
                            <li>Industry best practices</li>
                            <li>Advanced techniques</li>
                            <li>Practical implementation</li>
                            <li>Scaling strategies</li>
                    @endswitch
                </ul>
            </div>
            
            @if($progressPercentage < 100)
                <div class="sidebar-card">
                    <h3>Continue Learning</h3>
                    @php
                        $nextLesson = $lessons->first(function($lesson) use ($userProgress) {
                            $progress = $userProgress->get($lesson->id);
                            return !$progress || !$progress->is_completed;
                        });
                    @endphp
                    
                    @if($nextLesson)
                        <p>Next up: <strong>{{ $nextLesson->title }}</strong></p>
                        <a href="{{ route('lessons.show', [$campus, $course, $nextLesson]) }}" class="btn-continue">
                            <i class="fas fa-play"></i>
                            Continue Learning
                        </a>
                    @endif
                </div>
            @else
                <div class="sidebar-card completion">
                    <i class="fas fa-trophy"></i>
                    <h3>Course Completed!</h3>
                    <p>Congratulations! You've completed this course.</p>
                    <a href="{{ route('campuses.show', $campus) }}" class="btn-continue">
                        <i class="fas fa-arrow-left"></i>
                        Back to Campus
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
.course-page {
    max-width: 1200px;
    margin: 0 auto;
}

.course-header {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.breadcrumb {
    margin-bottom: 20px;
    color: #cccccc;
    font-size: 0.9rem;
}

.breadcrumb a {
    color: #FFD700;
    text-decoration: none;
}

.breadcrumb i {
    margin: 0 10px;
    font-size: 0.8rem;
}

.course-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 40px;
}

.course-details h1 {
    color: #FFD700;
    font-size: 2.2rem;
    margin-bottom: 15px;
}

.course-details p {
    color: #cccccc;
    margin-bottom: 25px;
    line-height: 1.6;
    max-width: 600px;
}

.course-meta {
    display: flex;
    gap: 30px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #999;
    font-size: 0.9rem;
}

.meta-item i {
    color: #FFD700;
}

.course-progress-card {
    display: flex;
    align-items: center;
    gap: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.progress-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: conic-gradient(#FFD700 calc(var(--progress) * 3.6deg), rgba(255,255,255,0.1) 0deg);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.progress-circle::before {
    content: '';
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #1a1a1a;
    position: absolute;
}

.progress-circle span {
    color: #ffffff;
    font-weight: bold;
    z-index: 1;
}

.progress-info h3 {
    color: #ffffff;
    margin-bottom: 5px;
}

.progress-info p {
    color: #cccccc;
    font-size: 0.9rem;
}

.course-content {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 30px;
}

.lessons-list h2 {
    color: #FFD700;
    margin-bottom: 25px;
    font-size: 1.8rem;
}

.lesson-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
}

.lesson-item:hover:not(.locked) {
    transform: translateY(-2px);
    border-color: #FFD700;
}

.lesson-item.completed {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
}

.lesson-item.locked {
    opacity: 0.6;
    cursor: not-allowed;
}

.lesson-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 215, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #FFD700;
    flex-shrink: 0;
}

.lesson-item.completed .lesson-number {
    background: #4CAF50;
    color: white;
}

.lesson-item.locked .lesson-number {
    background: rgba(255, 255, 255, 0.1);
    color: #666;
}

.lesson-content {
    flex: 1;
}

.lesson-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.lesson-header h3 {
    color: #ffffff;
    font-size: 1.1rem;
}

.lesson-badges {
    display: flex;
    gap: 8px;
}

.badge {
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: bold;
    text-transform: uppercase;
}

.badge.free {
    background: #4CAF50;
    color: white;
}

.badge.duration {
    background: rgba(255, 255, 255, 0.1);
    color: #cccccc;
}

.lesson-description {
    color: #cccccc;
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.lesson-progress {
    margin-top: 10px;
}

.progress-bar {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    height: 6px;
    margin-bottom: 5px;
    overflow: hidden;
}

.progress-fill {
    background: #FFD700;
    height: 100%;
    border-radius: 5px;
    transition: width 0.3s ease;
}

.progress-text {
    color: #999;
    font-size: 0.8rem;
}

.lesson-actions {
    flex-shrink: 0;
}

.btn-lesson {
    padding: 10px 20px;
    border: none;
    border-radius: 20px;
    background: #FFD700;
    color: #000000;
    text-decoration: none;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-lesson:hover:not(.locked) {
    background: #FFA500;
    transform: translateY(-2px);
}

.btn-lesson.locked {
    background: rgba(255, 255, 255, 0.1);
    color: #666;
    cursor: not-allowed;
}

.course-sidebar {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.sidebar-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
}

.sidebar-card h3 {
    color: #FFD700;
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.overview-stats {
    display: flex;
    justify-content: space-between;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: #FFD700;
}

.stat-label {
    color: #999;
    font-size: 0.8rem;
}

.learning-objectives {
    list-style: none;
}

.learning-objectives li {
    color: #cccccc;
    margin-bottom: 10px;
    position: relative;
    padding-left: 20px;
}

.learning-objectives li::before {
    content: '✓';
    color: #4CAF50;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.btn-continue {
    width: 100%;
    padding: 12px 20px;
    background: #FFD700;
    color: #000000;
    border: none;
    border-radius: 25px;
    font-weight: bold;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    margin-top: 15px;
}

.btn-continue:hover {
    background: #FFA500;
    transform: translateY(-2px);
}

.sidebar-card.completion {
    text-align: center;
    background: rgba(76, 175, 80, 0.1);
    border-color: #4CAF50;
}

.sidebar-card.completion i {
    font-size: 2.5rem;
    color: #FFD700;
    margin-bottom: 15px;
}

@media (max-width: 768px) {
    .course-info {
        flex-direction: column;
    }
    
    .course-content {
        grid-template-columns: 1fr;
    }
    
    .course-sidebar {
        order: -1;
    }
    
    .lesson-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .lesson-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .lesson-actions {
        width: 100%;
    }
    
    .btn-lesson {
        width: 100%;
        justify-content: center;
    }
}
</style>
@endsection
