@extends('layouts.admin')

@section('title', 'Content Management')

@section('content')
<div class="content-management">
    <!-- Page Header -->
    <div class="page-header">
        <div class="header-content">
            <h1>Content Management</h1>
            <p>Manage campuses, courses, lessons, and platform content</p>
        </div>
        <div class="header-actions">
            <button class="btn btn-primary" onclick="createContent()">
                <i class="fas fa-plus"></i>
                Add Content
            </button>
        </div>
    </div>

    <!-- Campus Overview -->
    <div class="content-section">
        <div class="section-header">
            <h2>Campus Overview</h2>
            <p>Manage all platform campuses and their content</p>
        </div>
        
        <div class="campus-grid">
            @foreach($campuses as $campus)
                <div class="campus-card">
                    <div class="campus-header">
                        <div class="campus-icon" style="color: {{ $campus->color }}">
                            <i class="{{ $campus->icon }}"></i>
                        </div>
                        <div class="campus-info">
                            <h3>{{ $campus->name }}</h3>
                            <p>{{ $campus->description }}</p>
                        </div>
                        <div class="campus-status">
                            <span class="status-badge {{ $campus->is_active ? 'active' : 'inactive' }}">
                                {{ $campus->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="campus-stats">
                        <div class="stat-item">
                            <span class="stat-number">{{ $campus->users_count }}</span>
                            <span class="stat-label">Students</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">{{ $campus->courses_count }}</span>
                            <span class="stat-label">Courses</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">{{ $campus->daily_tasks_count }}</span>
                            <span class="stat-label">Tasks</span>
                        </div>
                    </div>
                    
                    <div class="campus-actions">
                        <a href="{{ route('campuses.show', $campus) }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-eye"></i>
                            View
                        </a>
                        <button class="btn btn-primary btn-sm" onclick="editCampus({{ $campus->id }})">
                            <i class="fas fa-edit"></i>
                            Edit
                        </button>
                        <button class="btn {{ $campus->is_active ? 'btn-danger' : 'btn-success' }} btn-sm" 
                                onclick="toggleCampusStatus({{ $campus->id }}, {{ $campus->is_active ? 'false' : 'true' }})">
                            <i class="fas fa-{{ $campus->is_active ? 'pause' : 'play' }}"></i>
                            {{ $campus->is_active ? 'Disable' : 'Enable' }}
                        </button>
                    </div>
                </div>
            @endforeach
        </div>
    </div>

    <!-- Recent Courses -->
    <div class="content-section">
        <div class="section-header">
            <h2>Recent Courses</h2>
            <p>Latest courses added to the platform</p>
        </div>
        
        <div class="courses-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>Course</th>
                        <th>Campus</th>
                        <th>Lessons</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($recentCourses as $course)
                        <tr>
                            <td>
                                <div class="course-info">
                                    <h4>{{ $course->title }}</h4>
                                    <p>{{ Str::limit($course->description, 60) }}</p>
                                </div>
                            </td>
                            <td>
                                <div class="campus-tag" style="border-color: {{ $course->campus->color }}">
                                    <i class="{{ $course->campus->icon }}" style="color: {{ $course->campus->color }}"></i>
                                    {{ $course->campus->name }}
                                </div>
                            </td>
                            <td>
                                <span class="lesson-count">{{ $course->lessons->count() }}</span>
                            </td>
                            <td>
                                <span class="status-badge {{ $course->is_published ? 'active' : 'inactive' }}">
                                    {{ $course->is_published ? 'Published' : 'Draft' }}
                                </span>
                            </td>
                            <td>
                                <span class="date">{{ $course->created_at->format('M d, Y') }}</span>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-action view" onclick="viewCourse({{ $course->id }})" title="View Course">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn-action edit" onclick="editCourse({{ $course->id }})" title="Edit Course">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-action {{ $course->is_published ? 'unpublish' : 'publish' }}" 
                                            onclick="toggleCourseStatus({{ $course->id }}, {{ $course->is_published ? 'false' : 'true' }})"
                                            title="{{ $course->is_published ? 'Unpublish' : 'Publish' }} Course">
                                        <i class="fas fa-{{ $course->is_published ? 'eye-slash' : 'eye' }}"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <!-- Recent Lessons -->
    <div class="content-section">
        <div class="section-header">
            <h2>Recent Lessons</h2>
            <p>Latest lessons added to courses</p>
        </div>
        
        <div class="lessons-grid">
            @foreach($recentLessons as $lesson)
                <div class="lesson-card">
                    <div class="lesson-header">
                        <div class="lesson-type">
                            <i class="fas fa-{{ $lesson->type === 'video' ? 'play' : 'file-text' }}"></i>
                            {{ ucfirst($lesson->type) }}
                        </div>
                        <span class="status-badge {{ $lesson->is_published ? 'active' : 'inactive' }}">
                            {{ $lesson->is_published ? 'Published' : 'Draft' }}
                        </span>
                    </div>
                    
                    <div class="lesson-content">
                        <h4>{{ $lesson->title }}</h4>
                        <p>{{ Str::limit($lesson->description, 100) }}</p>
                        
                        <div class="lesson-meta">
                            <span class="course-name">{{ $lesson->course->title }}</span>
                            <span class="campus-name">{{ $lesson->course->campus->name }}</span>
                        </div>
                    </div>
                    
                    <div class="lesson-actions">
                        <button class="btn btn-secondary btn-sm" onclick="viewLesson({{ $lesson->id }})">
                            <i class="fas fa-eye"></i>
                            View
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="editLesson({{ $lesson->id }})">
                            <i class="fas fa-edit"></i>
                            Edit
                        </button>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</div>

<style>
.content-management {
    max-width: 1400px;
    margin: 0 auto;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-content h1 {
    color: #FFD700;
    font-size: 2rem;
    margin-bottom: 5px;
}

.header-content p {
    color: #cccccc;
    font-size: 1rem;
}

.content-section {
    margin-bottom: 50px;
}

.section-header {
    margin-bottom: 25px;
}

.section-header h2 {
    color: #FFD700;
    font-size: 1.5rem;
    margin-bottom: 5px;
}

.section-header p {
    color: #cccccc;
    font-size: 0.9rem;
}

.campus-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
}

.campus-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 25px;
    transition: all 0.3s ease;
}

.campus-card:hover {
    transform: translateY(-5px);
    border-color: rgba(255, 215, 0, 0.5);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.campus-header {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
}

.campus-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.campus-info {
    flex: 1;
}

.campus-info h3 {
    color: #ffffff;
    margin-bottom: 5px;
    font-size: 1.2rem;
}

.campus-info p {
    color: #cccccc;
    font-size: 0.9rem;
    line-height: 1.4;
}

.campus-status {
    flex-shrink: 0;
}

.status-badge {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: bold;
    text-transform: uppercase;
}

.status-badge.active { background: rgba(76, 175, 80, 0.2); color: #4CAF50; }
.status-badge.inactive { background: rgba(158, 158, 158, 0.2); color: #9e9e9e; }

.campus-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    color: #FFD700;
    font-size: 1.3rem;
    font-weight: bold;
}

.stat-label {
    color: #cccccc;
    font-size: 0.8rem;
}

.campus-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
}

.courses-table {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.course-info h4 {
    color: #ffffff;
    margin-bottom: 3px;
    font-size: 0.95rem;
}

.course-info p {
    color: #cccccc;
    font-size: 0.8rem;
    margin: 0;
}

.campus-tag {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 10px;
    border: 1px solid;
    border-radius: 12px;
    font-size: 0.8rem;
    color: #ffffff;
}

.lesson-count {
    color: #FFD700;
    font-weight: bold;
}

.date {
    color: #999;
    font-size: 0.8rem;
}

.action-buttons {
    display: flex;
    gap: 5px;
}

.btn-action {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.7rem;
}

.btn-action.view {
    background: rgba(33, 150, 243, 0.2);
    color: #2196F3;
}

.btn-action.edit {
    background: rgba(255, 152, 0, 0.2);
    color: #FF9800;
}

.btn-action.publish {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
}

.btn-action.unpublish {
    background: rgba(158, 158, 158, 0.2);
    color: #9e9e9e;
}

.btn-action:hover {
    transform: scale(1.1);
}

.lessons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.lesson-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    transition: all 0.3s ease;
}

.lesson-card:hover {
    transform: translateY(-3px);
    border-color: rgba(255, 215, 0, 0.5);
}

.lesson-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.lesson-type {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #FFD700;
    font-size: 0.8rem;
    font-weight: bold;
}

.lesson-content h4 {
    color: #ffffff;
    margin-bottom: 8px;
    font-size: 1rem;
}

.lesson-content p {
    color: #cccccc;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 12px;
}

.lesson-meta {
    display: flex;
    flex-direction: column;
    gap: 3px;
    margin-bottom: 15px;
}

.course-name {
    color: #FFD700;
    font-size: 0.8rem;
    font-weight: 500;
}

.campus-name {
    color: #999;
    font-size: 0.7rem;
}

.lesson-actions {
    display: flex;
    gap: 8px;
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .campus-grid {
        grid-template-columns: 1fr;
    }
    
    .campus-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .campus-stats {
        flex-direction: column;
        gap: 10px;
    }
    
    .lessons-grid {
        grid-template-columns: 1fr;
    }
    
    .courses-table {
        overflow-x: auto;
    }
}
</style>

<script>
function createContent() {
    alert('Content creation form would be implemented here');
}

function editCampus(campusId) {
    alert(`Edit campus ${campusId} form would be implemented here`);
}

function toggleCampusStatus(campusId, newStatus) {
    if (confirm(`Are you sure you want to ${newStatus === 'true' ? 'enable' : 'disable'} this campus?`)) {
        // In a real implementation, this would make an AJAX request
        alert(`Campus status would be updated to ${newStatus}`);
    }
}

function viewCourse(courseId) {
    alert(`View course ${courseId} details would be implemented here`);
}

function editCourse(courseId) {
    alert(`Edit course ${courseId} form would be implemented here`);
}

function toggleCourseStatus(courseId, newStatus) {
    if (confirm(`Are you sure you want to ${newStatus === 'true' ? 'publish' : 'unpublish'} this course?`)) {
        alert(`Course status would be updated to ${newStatus}`);
    }
}

function viewLesson(lessonId) {
    alert(`View lesson ${lessonId} details would be implemented here`);
}

function editLesson(lessonId) {
    alert(`Edit lesson ${lessonId} form would be implemented here`);
}
</script>
@endsection
