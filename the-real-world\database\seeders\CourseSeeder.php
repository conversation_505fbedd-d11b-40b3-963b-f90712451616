<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Campus;
use App\Models\Course;
use App\Models\Lesson;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get campuses
        $ecommerce = Campus::where('slug', 'ecommerce')->first();
        $copywriting = Campus::where('slug', 'copywriting')->first();
        $crypto = Campus::where('slug', 'crypto')->first();

        // Ecommerce Campus Courses
        if ($ecommerce) {
            $course1 = Course::create([
                'campus_id' => $ecommerce->id,
                'title' => 'Ecommerce Fundamentals',
                'slug' => 'ecommerce-fundamentals',
                'description' => 'Learn the basics of starting and running a successful ecommerce business.',
                'duration_minutes' => 180,
                'difficulty' => 'beginner',
                'is_published' => true,
                'sort_order' => 1,
            ]);

            // Lessons for Ecommerce Fundamentals
            Lesson::create([
                'course_id' => $course1->id,
                'title' => 'Introduction to Ecommerce',
                'slug' => 'introduction-to-ecommerce',
                'description' => 'Understanding the ecommerce landscape and opportunities.',
                'content' => 'In this lesson, you will learn about the fundamentals of ecommerce...',
                'video_url' => 'https://example.com/video1',
                'video_duration' => '15:30',
                'is_published' => true,
                'is_free' => true,
                'sort_order' => 1,
            ]);

            Lesson::create([
                'course_id' => $course1->id,
                'title' => 'Product Research Strategies',
                'slug' => 'product-research-strategies',
                'description' => 'How to find winning products that sell.',
                'content' => 'Product research is the foundation of any successful ecommerce business...',
                'video_url' => 'https://example.com/video2',
                'video_duration' => '22:45',
                'is_published' => true,
                'is_free' => false,
                'sort_order' => 2,
            ]);

            $course2 = Course::create([
                'campus_id' => $ecommerce->id,
                'title' => 'Advanced Scaling Strategies',
                'slug' => 'advanced-scaling-strategies',
                'description' => 'Take your ecommerce business to the next level with advanced techniques.',
                'duration_minutes' => 240,
                'difficulty' => 'advanced',
                'is_published' => true,
                'sort_order' => 2,
            ]);
        }

        // Copywriting Campus Courses
        if ($copywriting) {
            $course3 = Course::create([
                'campus_id' => $copywriting->id,
                'title' => 'Copywriting Mastery',
                'slug' => 'copywriting-mastery',
                'description' => 'Master the art of persuasive writing and sales copy.',
                'duration_minutes' => 200,
                'difficulty' => 'beginner',
                'is_published' => true,
                'sort_order' => 1,
            ]);

            // Lessons for Copywriting Mastery
            Lesson::create([
                'course_id' => $course3->id,
                'title' => 'Psychology of Persuasion',
                'slug' => 'psychology-of-persuasion',
                'description' => 'Understanding what makes people buy.',
                'content' => 'The psychology behind effective copywriting...',
                'video_url' => 'https://example.com/video3',
                'video_duration' => '18:20',
                'is_published' => true,
                'is_free' => true,
                'sort_order' => 1,
            ]);

            Lesson::create([
                'course_id' => $course3->id,
                'title' => 'Email Marketing Fundamentals',
                'slug' => 'email-marketing-fundamentals',
                'description' => 'Building effective email campaigns that convert.',
                'content' => 'Email marketing remains one of the highest ROI channels...',
                'video_url' => 'https://example.com/video4',
                'video_duration' => '25:15',
                'is_published' => true,
                'is_free' => false,
                'sort_order' => 2,
            ]);
        }

        // Crypto Campus Courses
        if ($crypto) {
            $course4 = Course::create([
                'campus_id' => $crypto->id,
                'title' => 'Crypto Trading Basics',
                'slug' => 'crypto-trading-basics',
                'description' => 'Learn the fundamentals of cryptocurrency trading.',
                'duration_minutes' => 160,
                'difficulty' => 'beginner',
                'is_published' => true,
                'sort_order' => 1,
            ]);

            // Lessons for Crypto Trading Basics
            Lesson::create([
                'course_id' => $course4->id,
                'title' => 'Understanding Blockchain',
                'slug' => 'understanding-blockchain',
                'description' => 'The technology behind cryptocurrencies.',
                'content' => 'Blockchain is the foundational technology...',
                'video_url' => 'https://example.com/video5',
                'video_duration' => '20:30',
                'is_published' => true,
                'is_free' => true,
                'sort_order' => 1,
            ]);

            Lesson::create([
                'course_id' => $course4->id,
                'title' => 'Technical Analysis for Crypto',
                'slug' => 'technical-analysis-for-crypto',
                'description' => 'Reading charts and making informed trading decisions.',
                'content' => 'Technical analysis is crucial for successful trading...',
                'video_url' => 'https://example.com/video6',
                'video_duration' => '28:45',
                'is_published' => true,
                'is_free' => false,
                'sort_order' => 2,
            ]);
        }
    }
}
