* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #ffffff;
    background-color: #000000;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 15px 0;
    border-bottom: 1px solid rgba(255, 215, 0, 0.2);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h2 {
    color: #FFD700;
    font-size: 24px;
    font-weight: bold;
    letter-spacing: 2px;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 30px;
}

.nav-links a {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: #FFD700;
}

.nav-btn {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: #000000 !important;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%);
    display: flex;
    align-items: center;
    padding-top: 80px;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 4rem;
    font-weight: bold;
    color: #FFD700;
    margin-bottom: 20px;
    letter-spacing: 3px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-subtitle {
    font-size: 1.8rem;
    color: #ffffff;
    margin-bottom: 20px;
    font-weight: 300;
}

.hero-description {
    font-size: 1.1rem;
    color: #cccccc;
    margin-bottom: 40px;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    margin-bottom: 60px;
}

.btn-primary {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: #000000;
    padding: 15px 30px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: bold;
    font-size: 16px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.4);
}

.btn-secondary {
    background: transparent;
    color: #ffffff;
    padding: 15px 30px;
    border: 2px solid #FFD700;
    border-radius: 30px;
    text-decoration: none;
    font-weight: bold;
    font-size: 16px;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #FFD700;
    color: #000000;
}

.hero-stats {
    display: flex;
    gap: 40px;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: #FFD700;
}

.stat-label {
    font-size: 0.9rem;
    color: #cccccc;
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 215, 0, 0.3);
    border-radius: 20px;
    padding: 60px 40px;
    text-align: center;
    backdrop-filter: blur(10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.hero-card i {
    font-size: 4rem;
    color: #FFD700;
    margin-bottom: 20px;
}

.hero-card h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #ffffff;
}

.hero-card p {
    color: #cccccc;
}

/* Features Section */
.features {
    padding: 100px 0;
    background: #111111;
}

.section-title {
    text-align: center;
    font-size: 3rem;
    color: #FFD700;
    margin-bottom: 60px;
    font-weight: bold;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
}

.feature-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
    border-color: #FFD700;
    box-shadow: 0 20px 40px rgba(255, 215, 0, 0.2);
}

.feature-card i {
    font-size: 3rem;
    color: #FFD700;
    margin-bottom: 20px;
}

.feature-card h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: #ffffff;
}

.feature-card p {
    color: #cccccc;
    line-height: 1.6;
}

/* Campuses Section */
.campuses {
    padding: 100px 0;
    background: #000000;
}

.campuses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.campus-card {
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 15px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.3s ease;
}

.campus-card:hover {
    transform: translateY(-5px);
    border-color: #FFD700;
    box-shadow: 0 15px 30px rgba(255, 215, 0, 0.3);
}

.campus-icon {
    font-size: 3rem;
    margin-bottom: 20px;
}

.campus-card h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: #FFD700;
}

.campus-card p {
    color: #cccccc;
}

/* Pricing Section */
.pricing {
    padding: 100px 0;
    background: #111111;
}

.pricing-card {
    max-width: 500px;
    margin: 0 auto;
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
    border: 2px solid #FFD700;
    border-radius: 20px;
    padding: 50px 40px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(255, 215, 0, 0.3);
}

.price-header h3 {
    font-size: 1.8rem;
    color: #FFD700;
    margin-bottom: 20px;
}

.price {
    margin-bottom: 40px;
}

.currency {
    font-size: 2rem;
    color: #ffffff;
    vertical-align: top;
}

.amount {
    font-size: 4rem;
    font-weight: bold;
    color: #FFD700;
}

.period {
    font-size: 1.2rem;
    color: #cccccc;
}

.features-list {
    list-style: none;
    margin-bottom: 40px;
}

.features-list li {
    padding: 10px 0;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.features-list i {
    color: #FFD700;
    margin-right: 15px;
    font-size: 1.2rem;
}

/* Footer */
.footer {
    background: #000000;
    padding: 60px 0 20px;
    border-top: 1px solid rgba(255, 215, 0, 0.2);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h3,
.footer-section h4 {
    color: #FFD700;
    margin-bottom: 20px;
}

.footer-section p {
    color: #cccccc;
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: #cccccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #FFD700;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: #cccccc;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .nav-links {
        display: none;
    }
    
    .features-grid,
    .campuses-grid {
        grid-template-columns: 1fr;
    }
}
