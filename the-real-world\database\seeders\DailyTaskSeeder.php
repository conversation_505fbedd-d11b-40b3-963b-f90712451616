<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Campus;
use App\Models\DailyTask;
use Carbon\Carbon;

class DailyTaskSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $campuses = Campus::all();
        $today = Carbon::today();

        foreach ($campuses as $campus) {
            // Create today's task
            $todayTask = $this->getTaskForCampus($campus->slug, $today);
            if ($todayTask) {
                DailyTask::create([
                    'campus_id' => $campus->id,
                    'title' => $todayTask['title'],
                    'description' => $todayTask['description'],
                    'instructions' => $todayTask['instructions'],
                    'points_reward' => $todayTask['points_reward'],
                    'task_date' => $today,
                    'difficulty' => $todayTask['difficulty'],
                    'is_active' => true,
                ]);
            }

            // Create yesterday's task (for testing)
            $yesterdayTask = $this->getTaskForCampus($campus->slug, $today->copy()->subDay());
            if ($yesterdayTask) {
                DailyTask::create([
                    'campus_id' => $campus->id,
                    'title' => $yesterdayTask['title'],
                    'description' => $yesterdayTask['description'],
                    'instructions' => $yesterdayTask['instructions'],
                    'points_reward' => $yesterdayTask['points_reward'],
                    'task_date' => $today->copy()->subDay(),
                    'difficulty' => $yesterdayTask['difficulty'],
                    'is_active' => true,
                ]);
            }
        }
    }

    private function getTaskForCampus($campusSlug, $date)
    {
        $tasks = [
            'ecommerce' => [
                [
                    'title' => 'Product Research Challenge',
                    'description' => 'Find 3 trending products in your niche using research tools.',
                    'instructions' => 'Use tools like Google Trends, Amazon Best Sellers, or AliExpress to find 3 products with high demand and low competition. Document your findings with screenshots and reasoning.',
                    'points_reward' => 15,
                    'difficulty' => 'medium',
                ],
                [
                    'title' => 'Competitor Analysis',
                    'description' => 'Analyze 2 successful competitors in your market.',
                    'instructions' => 'Choose 2 successful stores in your niche. Analyze their product pages, pricing strategy, marketing approach, and customer reviews. Create a summary report.',
                    'points_reward' => 20,
                    'difficulty' => 'hard',
                ],
            ],
            'copywriting' => [
                [
                    'title' => 'Email Subject Line Challenge',
                    'description' => 'Write 10 compelling email subject lines for a product launch.',
                    'instructions' => 'Choose any product and write 10 different subject lines that would make people want to open the email. Use different psychological triggers and techniques.',
                    'points_reward' => 10,
                    'difficulty' => 'easy',
                ],
                [
                    'title' => 'Sales Page Headline',
                    'description' => 'Create a powerful headline for a sales page.',
                    'instructions' => 'Write a headline for a sales page that clearly communicates the main benefit, creates urgency, and appeals to your target audience. Include 3 variations.',
                    'points_reward' => 15,
                    'difficulty' => 'medium',
                ],
            ],
            'crypto' => [
                [
                    'title' => 'Market Analysis',
                    'description' => 'Analyze the current market trends for top 5 cryptocurrencies.',
                    'instructions' => 'Research Bitcoin, Ethereum, and 3 other top cryptocurrencies. Analyze their price movements, news, and technical indicators. Provide a brief market outlook.',
                    'points_reward' => 20,
                    'difficulty' => 'hard',
                ],
                [
                    'title' => 'Portfolio Diversification',
                    'description' => 'Create a balanced crypto portfolio allocation.',
                    'instructions' => 'Design a hypothetical $10,000 crypto portfolio with proper diversification across different categories (large cap, mid cap, DeFi, etc.). Explain your reasoning.',
                    'points_reward' => 15,
                    'difficulty' => 'medium',
                ],
            ],
        ];

        $campusTasks = $tasks[$campusSlug] ?? $tasks['ecommerce'];
        $dayIndex = $date->dayOfYear % count($campusTasks);

        return $campusTasks[$dayIndex];
    }
}
