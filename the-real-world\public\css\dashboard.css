* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body.dashboard-body {
    font-family: 'Arial', sans-serif;
    background: #0a0a0a;
    color: #ffffff;
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: linear-gradient(180deg, #1a1a1a 0%, #0f0f0f 100%);
    border-right: 1px solid rgba(255, 215, 0, 0.2);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
}

.sidebar-header {
    padding: 30px 20px;
    border-bottom: 1px solid rgba(255, 215, 0, 0.2);
    text-align: center;
}

.sidebar-header h2 {
    color: #FFD700;
    font-size: 20px;
    font-weight: bold;
    letter-spacing: 2px;
}

.sidebar-nav ul {
    list-style: none;
    padding: 20px 0;
}

.nav-section {
    padding: 20px 20px 10px;
}

.section-title {
    color: #666;
    font-size: 12px;
    font-weight: bold;
    letter-spacing: 1px;
}

.nav-item a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: #cccccc;
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-item a:hover,
.nav-item.active a {
    background: rgba(255, 215, 0, 0.1);
    color: #FFD700;
    border-left-color: #FFD700;
}

.nav-item.admin-only {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 15px;
    padding-top: 15px;
}

.nav-item.admin-only a {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
    border: 1px solid rgba(244, 67, 54, 0.3);
    border-left: 3px solid #f44336;
}

.nav-item.admin-only a:hover {
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
}

.nav-item a i {
    width: 20px;
    margin-right: 15px;
    font-size: 16px;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
}

/* Header */
.dashboard-header {
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 215, 0, 0.2);
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 18px;
    cursor: pointer;
    display: none;
}

.header-left h1 {
    color: #ffffff;
    font-size: 24px;
    font-weight: 600;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: 15px;
    color: #666;
}

.search-box input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 10px 15px 10px 45px;
    color: #ffffff;
    width: 300px;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #FFD700;
    background: rgba(255, 255, 255, 0.15);
}

.notification-btn {
    position: relative;
    background: none;
    border: none;
    color: #ffffff;
    font-size: 20px;
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.notification-btn:hover {
    background: rgba(255, 215, 0, 0.1);
    color: #FFD700;
}

.notification-badge {
    position: absolute;
    top: 5px;
    right: 5px;
    background: #ff4444;
    color: #ffffff;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-menu {
    position: relative;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.user-info:hover {
    background: rgba(255, 215, 0, 0.1);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid #FFD700;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    color: #ffffff;
}

.user-level {
    font-size: 12px;
    color: #FFD700;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: #1a1a1a;
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 10px;
    padding: 10px 0;
    min-width: 180px;
    display: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.user-menu:hover .user-dropdown {
    display: block;
}

.user-dropdown a,
.user-dropdown button {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    color: #cccccc;
    text-decoration: none;
    background: none;
    border: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    transition: all 0.3s ease;
}

.user-dropdown a:hover,
.user-dropdown button:hover {
    background: rgba(255, 215, 0, 0.1);
    color: #FFD700;
}

.user-dropdown hr {
    border: none;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin: 5px 0;
}

/* Page Content */
.page-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

/* Dashboard Content */
.welcome-section {
    margin-bottom: 40px;
}

.welcome-card {
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
    border: 1px solid rgba(255, 215, 0, 0.3);
    border-radius: 15px;
    padding: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.welcome-text h1 {
    color: #FFD700;
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.welcome-text p {
    color: #cccccc;
    font-size: 1.2rem;
}

.welcome-stats {
    display: flex;
    gap: 40px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: bold;
    color: #FFD700;
}

.stat-label {
    color: #cccccc;
    font-size: 0.9rem;
}

/* Quick Actions */
.quick-actions {
    margin-bottom: 40px;
}

.quick-actions h2 {
    color: #ffffff;
    margin-bottom: 20px;
    font-size: 1.8rem;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.action-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    text-decoration: none;
    color: #ffffff;
    transition: all 0.3s ease;
}

.action-card:hover {
    transform: translateY(-5px);
    border-color: #FFD700;
    box-shadow: 0 15px 30px rgba(255, 215, 0, 0.2);
}

.action-card i {
    font-size: 2.5rem;
    color: #FFD700;
    margin-bottom: 15px;
}

.action-card h3 {
    margin-bottom: 10px;
    color: #ffffff;
}

.action-card p {
    color: #cccccc;
    font-size: 0.9rem;
}

/* Recent Activity */
.recent-activity {
    margin-bottom: 40px;
}

.recent-activity h2 {
    color: #ffffff;
    margin-bottom: 20px;
    font-size: 1.8rem;
}

.activity-list {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 215, 0, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.activity-icon i {
    color: #FFD700;
    font-size: 1.2rem;
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    color: #ffffff;
    margin-bottom: 5px;
}

.activity-content p {
    color: #cccccc;
    margin-bottom: 5px;
}

.activity-time {
    color: #666;
    font-size: 0.8rem;
}

.activity-points {
    color: #FFD700;
    font-weight: bold;
}

/* Progress Overview */
.progress-overview h2 {
    color: #ffffff;
    margin-bottom: 20px;
    font-size: 1.8rem;
}

.progress-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.progress-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
}

.progress-card h3 {
    color: #FFD700;
    margin-bottom: 15px;
}

.progress-bar {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    height: 10px;
    margin-bottom: 10px;
    overflow: hidden;
}

.progress-fill {
    background: linear-gradient(90deg, #FFD700 0%, #FFA500 100%);
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

.progress-text {
    color: #cccccc;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .sidebar-toggle {
        display: block;
    }
    
    .search-box input {
        width: 200px;
    }
    
    .welcome-card {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }
    
    .welcome-stats {
        justify-content: center;
    }
}
