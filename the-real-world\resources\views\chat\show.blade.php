@extends('layouts.dashboard')

@section('title', $chatRoom->name . ' - ' . $campus->name)

@section('content')
<div class="chat-page">
    <!-- Chat Header -->
    <div class="chat-header">
        <div class="chat-info">
            <div class="breadcrumb">
                <a href="{{ route('campuses.show', $campus) }}">{{ $campus->name }}</a>
                <i class="fas fa-chevron-right"></i>
                <span>{{ $chatRoom->name }}</span>
            </div>
            <h1>{{ $chatRoom->name }}</h1>
            <p>{{ $chatRoom->description }}</p>
        </div>
        
        <div class="chat-stats">
            <div class="stat">
                <i class="fas fa-circle online"></i>
                <span>{{ $onlineCount }} online</span>
            </div>
            <div class="stat">
                <i class="fas fa-users"></i>
                <span>{{ $campus->users()->count() }} members</span>
            </div>
            <div class="room-type {{ $chatRoom->type }}">
                {{ ucfirst($chatRoom->type) }}
            </div>
        </div>
    </div>

    <!-- Chat Container -->
    <div class="chat-container">
        <!-- Messages Area -->
        <div class="messages-container">
            <div class="messages-area" id="messagesArea">
                @foreach($messages as $message)
                    <div class="message {{ $message->user_id === Auth::id() ? 'own' : '' }}">
                        <div class="message-avatar">
                            @if($message->user->avatar)
                                <img src="{{ $message->user->avatar }}" alt="{{ $message->user->name }}">
                            @else
                                <div class="default-avatar">
                                    {{ substr($message->user->name, 0, 1) }}
                                </div>
                            @endif
                            @if($message->user->role === 'professor' || $message->user->role === 'admin')
                                <div class="role-badge {{ $message->user->role }}">
                                    @if($message->user->role === 'professor')
                                        <i class="fas fa-graduation-cap"></i>
                                    @else
                                        <i class="fas fa-crown"></i>
                                    @endif
                                </div>
                            @endif
                        </div>
                        
                        <div class="message-content">
                            <div class="message-header">
                                <span class="username {{ $message->user->role }}">{{ $message->user->name }}</span>
                                <span class="timestamp">{{ $message->created_at->format('H:i') }}</span>
                            </div>
                            <div class="message-text">{{ $message->content }}</div>
                        </div>
                    </div>
                @endforeach
            </div>
            
            <!-- Typing Indicator -->
            <div class="typing-indicator" id="typingIndicator" style="display: none;">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <span class="typing-text">Someone is typing...</span>
            </div>
        </div>

        <!-- Message Input -->
        <div class="message-input-container">
            <form id="messageForm" class="message-form">
                @csrf
                <div class="input-wrapper">
                    <input type="text" id="messageInput" placeholder="Type your message..." maxlength="1000" autocomplete="off">
                    <button type="submit" id="sendButton">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </form>
            
            <div class="chat-rules">
                <i class="fas fa-info-circle"></i>
                <span>Be respectful and follow community guidelines</span>
            </div>
        </div>
    </div>

    <!-- Online Users Sidebar -->
    <div class="online-users-sidebar">
        <h3>Online Now ({{ $onlineCount }})</h3>
        <div class="users-list" id="usersList">
            <!-- Simulated online users -->
            <div class="online-user">
                <div class="user-avatar">
                    <div class="default-avatar">{{ substr(Auth::user()->name, 0, 1) }}</div>
                    <div class="online-dot"></div>
                </div>
                <div class="user-info">
                    <span class="user-name">{{ Auth::user()->name }} (You)</span>
                    <span class="user-status">{{ ucfirst(Auth::user()->role) }}</span>
                </div>
            </div>
            
            @for($i = 0; $i < min(15, $onlineCount - 1); $i++)
                <div class="online-user">
                    <div class="user-avatar">
                        <div class="default-avatar">{{ chr(65 + $i) }}</div>
                        <div class="online-dot"></div>
                    </div>
                    <div class="user-info">
                        <span class="user-name">Student {{ $i + 1 }}</span>
                        <span class="user-status">Student</span>
                    </div>
                </div>
            @endfor
        </div>
    </div>
</div>

<style>
.chat-page {
    display: grid;
    grid-template-columns: 1fr 300px;
    grid-template-rows: auto 1fr;
    gap: 20px;
    height: calc(100vh - 140px);
    max-width: 1400px;
    margin: 0 auto;
}

.chat-header {
    grid-column: 1 / -1;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.breadcrumb {
    margin-bottom: 10px;
    color: #cccccc;
    font-size: 0.9rem;
}

.breadcrumb a {
    color: #FFD700;
    text-decoration: none;
}

.breadcrumb i {
    margin: 0 10px;
    font-size: 0.8rem;
}

.chat-info h1 {
    color: #FFD700;
    font-size: 1.8rem;
    margin-bottom: 5px;
}

.chat-info p {
    color: #cccccc;
    margin: 0;
}

.chat-stats {
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #cccccc;
    font-size: 0.9rem;
}

.stat i.online {
    color: #4CAF50;
}

.room-type {
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
}

.room-type.general { background: #2196F3; color: white; }
.room-type.announcements { background: #FF9800; color: white; }
.room-type.help { background: #4CAF50; color: white; }
.room-type.wins { background: #FFD700; color: black; }

.chat-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.messages-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.messages-area {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.message {
    display: flex;
    gap: 12px;
    max-width: 80%;
}

.message.own {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message.own .message-content {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: #000000;
}

.message-avatar {
    position: relative;
    flex-shrink: 0;
}

.message-avatar img,
.default-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.default-avatar {
    background: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.role-badge {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    border: 2px solid #1a1a1a;
}

.role-badge.professor {
    background: #2196F3;
    color: white;
}

.role-badge.admin {
    background: #FFD700;
    color: black;
}

.message-content {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 12px 16px;
    max-width: 100%;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 4px;
}

.username {
    font-weight: bold;
    font-size: 0.9rem;
}

.username.professor {
    color: #2196F3;
}

.username.admin {
    color: #FFD700;
}

.timestamp {
    color: #999;
    font-size: 0.8rem;
}

.message-text {
    color: #ffffff;
    line-height: 1.4;
    word-wrap: break-word;
}

.message.own .message-text {
    color: #000000;
}

.typing-indicator {
    padding: 10px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #999;
    font-size: 0.9rem;
}

.typing-dots {
    display: flex;
    gap: 3px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #999;
    animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-10px);
    }
}

.message-input-container {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px;
}

.message-form {
    margin-bottom: 10px;
}

.input-wrapper {
    display: flex;
    gap: 10px;
    align-items: center;
}

#messageInput {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 12px 20px;
    color: #ffffff;
    font-size: 0.9rem;
    outline: none;
    transition: all 0.3s ease;
}

#messageInput:focus {
    border-color: #FFD700;
    background: rgba(255, 255, 255, 0.15);
}

#sendButton {
    background: #FFD700;
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000000;
    cursor: pointer;
    transition: all 0.3s ease;
}

#sendButton:hover {
    background: #FFA500;
    transform: scale(1.05);
}

.chat-rules {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #999;
    font-size: 0.8rem;
}

.online-users-sidebar {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px;
    overflow: hidden;
}

.online-users-sidebar h3 {
    color: #FFD700;
    margin-bottom: 20px;
    font-size: 1.1rem;
}

.users-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: calc(100vh - 300px);
    overflow-y: auto;
}

.online-user {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.online-user:hover {
    background: rgba(255, 255, 255, 0.05);
}

.user-avatar {
    position: relative;
}

.user-avatar .default-avatar {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
}

.online-dot {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 10px;
    height: 10px;
    background: #4CAF50;
    border-radius: 50%;
    border: 2px solid #1a1a1a;
}

.user-info {
    flex: 1;
    min-width: 0;
}

.user-name {
    display: block;
    color: #ffffff;
    font-size: 0.9rem;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-status {
    display: block;
    color: #999;
    font-size: 0.8rem;
}

@media (max-width: 768px) {
    .chat-page {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
    }
    
    .online-users-sidebar {
        order: 3;
        max-height: 200px;
    }
    
    .chat-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .chat-stats {
        align-self: stretch;
        justify-content: space-between;
    }
    
    .message {
        max-width: 90%;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const messageForm = document.getElementById('messageForm');
    const messageInput = document.getElementById('messageInput');
    const messagesArea = document.getElementById('messagesArea');
    const typingIndicator = document.getElementById('typingIndicator');
    
    let typingTimer;
    let isTyping = false;
    
    // Auto-scroll to bottom
    function scrollToBottom() {
        messagesArea.scrollTop = messagesArea.scrollHeight;
    }
    
    // Initial scroll to bottom
    scrollToBottom();
    
    // Send message
    messageForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const content = messageInput.value.trim();
        if (!content) return;
        
        // Disable input while sending
        messageInput.disabled = true;
        
        fetch(`{{ route('chat.send-message', [$campus, $chatRoom]) }}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ content: content })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Add message to chat
                addMessageToChat(data.message);
                messageInput.value = '';
                scrollToBottom();
            }
        })
        .catch(error => {
            console.error('Error sending message:', error);
            if (window.showNotification) {
                window.showNotification('Failed to send message', 'error');
            }
        })
        .finally(() => {
            messageInput.disabled = false;
            messageInput.focus();
        });
    });
    
    // Add message to chat
    function addMessageToChat(message) {
        const messageElement = document.createElement('div');
        messageElement.className = `message ${message.is_own ? 'own' : ''}`;
        
        messageElement.innerHTML = `
            <div class="message-avatar">
                ${message.user.avatar ? 
                    `<img src="${message.user.avatar}" alt="${message.user.name}">` :
                    `<div class="default-avatar">${message.user.name.charAt(0)}</div>`
                }
                ${message.user.role === 'professor' || message.user.role === 'admin' ? 
                    `<div class="role-badge ${message.user.role}">
                        <i class="fas fa-${message.user.role === 'professor' ? 'graduation-cap' : 'crown'}"></i>
                    </div>` : ''
                }
            </div>
            <div class="message-content">
                <div class="message-header">
                    <span class="username ${message.user.role}">${message.user.name}</span>
                    <span class="timestamp">${message.created_at}</span>
                </div>
                <div class="message-text">${message.content}</div>
            </div>
        `;
        
        messagesArea.appendChild(messageElement);
    }
    
    // Typing indicator (simulated)
    messageInput.addEventListener('input', function() {
        if (!isTyping) {
            isTyping = true;
            // In a real implementation, you would emit a typing event here
        }
        
        clearTimeout(typingTimer);
        typingTimer = setTimeout(() => {
            isTyping = false;
            // In a real implementation, you would emit a stop typing event here
        }, 1000);
    });
    
    // Simulate receiving messages (in a real app, this would be WebSocket/Pusher)
    function simulateIncomingMessage() {
        if (Math.random() < 0.1) { // 10% chance every 10 seconds
            const messages = [
                "Great lesson today!",
                "Anyone have experience with this?",
                "Thanks for the help!",
                "Just made my first sale! 🎉",
                "The community here is amazing",
                "Professor, could you clarify this point?",
                "Working on my daily task now",
                "This strategy really works!"
            ];
            
            const randomMessage = messages[Math.floor(Math.random() * messages.length)];
            const fakeMessage = {
                id: Date.now(),
                content: randomMessage,
                user: {
                    id: Math.floor(Math.random() * 1000),
                    name: `Student ${Math.floor(Math.random() * 100)}`,
                    username: `student${Math.floor(Math.random() * 100)}`,
                    avatar: null,
                    role: 'student'
                },
                created_at: new Date().toLocaleTimeString('en-US', { hour12: false, hour: '2-digit', minute: '2-digit' }),
                is_own: false
            };
            
            addMessageToChat(fakeMessage);
            scrollToBottom();
        }
    }
    
    // Simulate incoming messages every 10 seconds
    setInterval(simulateIncomingMessage, 10000);
    
    // Focus on message input
    messageInput.focus();
});
</script>
@endsection
