<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Campus;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample users for leaderboard
        $users = [
            [
                'name' => '<PERSON>',
                'username' => 'alex_thompson',
                'email' => '<EMAIL>',
                'points' => 2500,
                'level' => 15,
                'role' => 'student',
            ],
            [
                'name' => '<PERSON>',
                'username' => 'sarah_j',
                'email' => '<EMAIL>',
                'points' => 2200,
                'level' => 13,
                'role' => 'student',
            ],
            [
                'name' => '<PERSON>',
                'username' => 'mike_chen',
                'email' => '<EMAIL>',
                'points' => 1950,
                'level' => 12,
                'role' => 'student',
            ],
            [
                'name' => '<PERSON>',
                'username' => 'emma_w',
                'email' => '<EMAIL>',
                'points' => 1800,
                'level' => 11,
                'role' => 'student',
            ],
            [
                'name' => '<PERSON>',
                'username' => 'david_r',
                'email' => '<EMAIL>',
                'points' => 1650,
                'level' => 10,
                'role' => 'student',
            ],
            [
                'name' => 'Lisa Park',
                'username' => 'lisa_park',
                'email' => '<EMAIL>',
                'points' => 1500,
                'level' => 9,
                'role' => 'student',
            ],
            [
                'name' => 'James Miller',
                'username' => 'james_m',
                'email' => '<EMAIL>',
                'points' => 1350,
                'level' => 8,
                'role' => 'student',
            ],
            [
                'name' => 'Professor Anderson',
                'username' => 'prof_anderson',
                'email' => '<EMAIL>',
                'points' => 3000,
                'level' => 20,
                'role' => 'professor',
            ],
        ];

        foreach ($users as $userData) {
            $user = User::create([
                'name' => $userData['name'],
                'username' => $userData['username'],
                'email' => $userData['email'],
                'password' => Hash::make('password'),
                'points' => $userData['points'],
                'level' => $userData['level'],
                'role' => $userData['role'],
                'is_active' => true,
            ]);

            // Enroll users in random campuses
            $campuses = Campus::inRandomOrder()->limit(rand(2, 4))->get();
            foreach ($campuses as $campus) {
                $user->campuses()->attach($campus->id, [
                    'enrolled_at' => now()->subDays(rand(1, 30)),
                    'is_active' => true,
                ]);
            }
        }
    }
}
