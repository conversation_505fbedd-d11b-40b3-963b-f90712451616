<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('task_submissions', function (Blueprint $table) {
            if (!Schema::hasColumn('task_submissions', 'reviewed_at')) {
                $table->timestamp('reviewed_at')->nullable()->after('points_awarded');
            }
            if (!Schema::hasColumn('task_submissions', 'reviewed_by')) {
                $table->foreignId('reviewed_by')->nullable()->constrained('users')->after('reviewed_at');
            }
            if (!Schema::hasColumn('task_submissions', 'attachments')) {
                $table->json('attachments')->nullable()->after('submission_content');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('task_submissions', function (Blueprint $table) {
            $table->dropColumn(['reviewed_at', 'reviewed_by', 'attachments']);
        });
    }
};
