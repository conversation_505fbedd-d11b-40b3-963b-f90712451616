<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Campus;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules\Password;

class ProfileController extends Controller
{
    /**
     * Display the user's profile.
     */
    public function show(User $user = null)
    {
        $user = $user ?? Auth::user();

        // Get user's enrolled campuses with progress
        $enrolledCampuses = $user->campuses()->with(['courses.lessons'])->get()->map(function ($campus) use ($user) {
            $totalLessons = $campus->courses->sum(function ($course) {
                return $course->lessons->where('is_published', true)->count();
            });

            $completedLessons = $user->progress()
                ->whereHas('lesson.course', function ($query) use ($campus) {
                    $query->where('campus_id', $campus->id);
                })
                ->where('is_completed', true)
                ->count();

            $campus->progress_percentage = $totalLessons > 0 ? round(($completedLessons / $totalLessons) * 100) : 0;
            $campus->completed_lessons = $completedLessons;
            $campus->total_lessons = $totalLessons;

            return $campus;
        });

        // Get user's recent activity
        $recentActivity = collect();

        // Recent lesson completions
        $recentLessons = $user->progress()
            ->with(['lesson.course.campus'])
            ->where('is_completed', true)
            ->orderBy('completed_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($progress) {
                return [
                    'type' => 'lesson_completed',
                    'title' => 'Completed lesson: ' . $progress->lesson->title,
                    'description' => 'In ' . $progress->lesson->course->campus->name,
                    'date' => $progress->completed_at,
                    'points' => 25,
                    'icon' => 'fas fa-graduation-cap',
                ];
            });

        // Recent task submissions
        $recentTasks = $user->taskSubmissions()
            ->with(['dailyTask.campus'])
            ->where('status', 'approved')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($submission) {
                return [
                    'type' => 'task_completed',
                    'title' => 'Completed task: ' . $submission->dailyTask->title,
                    'description' => 'In ' . $submission->dailyTask->campus->name,
                    'date' => $submission->created_at,
                    'points' => $submission->points_awarded,
                    'icon' => 'fas fa-tasks',
                ];
            });

        $recentActivity = $recentLessons->merge($recentTasks)
            ->sortByDesc('date')
            ->take(10);

        // Get user's achievements (simulated)
        $achievements = $this->getUserAchievements($user);

        // Get user's stats
        $stats = [
            'total_points' => $user->points,
            'level' => $user->level,
            'completed_lessons' => $user->progress()->where('is_completed', true)->count(),
            'completed_tasks' => $user->taskSubmissions()->where('status', 'approved')->count(),
            'enrolled_campuses' => $user->campuses()->count(),
            'join_date' => $user->created_at,
        ];

        return view('profile.show', compact('user', 'enrolledCampuses', 'recentActivity', 'achievements', 'stats'));
    }

    /**
     * Show the form for editing the user's profile.
     */
    public function edit()
    {
        $user = Auth::user();
        return view('profile.edit', compact('user'));
    }

    /**
     * Update the user's profile information.
     */
    public function update(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:users,username,' . $user->id,
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'bio' => 'nullable|string|max:500',
            'location' => 'nullable|string|max:100',
            'website' => 'nullable|url|max:255',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->only(['name', 'username', 'email', 'bio', 'location', 'website']);

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists
            if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
                Storage::disk('public')->delete($user->avatar);
            }

            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $data['avatar'] = $avatarPath;
        }

        $user->update($data);

        return redirect()->route('profile.show')->with('success', 'Profile updated successfully!');
    }

    /**
     * Update the user's password.
     */
    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required|current_password',
            'password' => ['required', 'confirmed', Password::min(8)],
        ]);

        Auth::user()->update([
            'password' => Hash::make($request->password),
        ]);

        return back()->with('success', 'Password updated successfully!');
    }

    /**
     * Get user's achievements (simulated).
     */
    private function getUserAchievements(User $user)
    {
        $achievements = [];

        // First Steps
        if ($user->campuses()->count() > 0) {
            $achievements[] = [
                'id' => 'first_steps',
                'title' => 'First Steps',
                'description' => 'Enrolled in your first campus',
                'icon' => '🎯',
                'earned' => true,
                'earned_at' => $user->campuses()->first()->pivot->enrolled_at ?? $user->created_at,
            ];
        }

        // Task Master
        $completedTasks = $user->taskSubmissions()->where('status', 'approved')->count();
        if ($completedTasks >= 10) {
            $achievements[] = [
                'id' => 'task_master',
                'title' => 'Task Master',
                'description' => 'Complete 10 daily tasks',
                'icon' => '🔥',
                'earned' => true,
                'earned_at' => $user->taskSubmissions()->where('status', 'approved')->orderBy('created_at')->skip(9)->first()->created_at ?? now(),
            ];
        }

        // Learning Machine
        $completedLessons = $user->progress()->where('is_completed', true)->count();
        if ($completedLessons >= 25) {
            $achievements[] = [
                'id' => 'learning_machine',
                'title' => 'Learning Machine',
                'description' => 'Complete 25 lessons',
                'icon' => '📚',
                'earned' => true,
                'earned_at' => $user->progress()->where('is_completed', true)->orderBy('completed_at')->skip(24)->first()->completed_at ?? now(),
            ];
        }

        // Point Collector
        if ($user->points >= 1000) {
            $achievements[] = [
                'id' => 'point_collector',
                'title' => 'Point Collector',
                'description' => 'Earn 1,000 points',
                'icon' => '⭐',
                'earned' => true,
                'earned_at' => now()->subDays(rand(1, 30)),
            ];
        }

        // Campus Explorer
        if ($user->campuses()->count() >= 3) {
            $achievements[] = [
                'id' => 'campus_explorer',
                'title' => 'Campus Explorer',
                'description' => 'Enroll in 3 different campuses',
                'icon' => '🗺️',
                'earned' => true,
                'earned_at' => $user->campuses()->orderBy('user_campus_enrollments.enrolled_at')->skip(2)->first()->pivot->enrolled_at ?? now(),
            ];
        }

        // Add some locked achievements
        if ($completedTasks < 50) {
            $achievements[] = [
                'id' => 'task_legend',
                'title' => 'Task Legend',
                'description' => 'Complete 50 daily tasks',
                'icon' => '👑',
                'earned' => false,
                'progress' => $completedTasks,
                'target' => 50,
            ];
        }

        if ($user->points < 5000) {
            $achievements[] = [
                'id' => 'point_master',
                'title' => 'Point Master',
                'description' => 'Earn 5,000 points',
                'icon' => '💎',
                'earned' => false,
                'progress' => $user->points,
                'target' => 5000,
            ];
        }

        return collect($achievements);
    }
}
