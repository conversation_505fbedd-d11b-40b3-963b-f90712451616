<?php $__env->startSection('title', $user->name . ' - Profile'); ?>

<?php $__env->startSection('content'); ?>
<div class="profile-page">
    <!-- Profile Header -->
    <div class="profile-header">
        <div class="profile-banner">
            <div class="banner-overlay"></div>
        </div>
        
        <div class="profile-info">
            <div class="profile-avatar">
                <?php if($user->avatar): ?>
                    <img src="<?php echo e(Storage::url($user->avatar)); ?>" alt="<?php echo e($user->name); ?>">
                <?php else: ?>
                    <div class="default-avatar"><?php echo e(substr($user->name, 0, 1)); ?></div>
                <?php endif; ?>
                <?php if($user->id === Auth::id()): ?>
                    <div class="avatar-edit">
                        <i class="fas fa-camera"></i>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="profile-details">
                <div class="profile-main">
                    <h1><?php echo e($user->name); ?></h1>
                    <?php if($user->username): ?>
                        <p class="username">{{ $user->username }}</p>
                    <?php endif; ?>
                    <?php if($user->bio): ?>
                        <p class="bio"><?php echo e($user->bio); ?></p>
                    <?php endif; ?>
                    
                    <div class="profile-meta">
                        <?php if($user->location): ?>
                            <span class="meta-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <?php echo e($user->location); ?>

                            </span>
                        <?php endif; ?>
                        <?php if($user->website): ?>
                            <span class="meta-item">
                                <i class="fas fa-link"></i>
                                <a href="<?php echo e($user->website); ?>" target="_blank"><?php echo e($user->website); ?></a>
                            </span>
                        <?php endif; ?>
                        <span class="meta-item">
                            <i class="fas fa-calendar"></i>
                            Joined <?php echo e($stats['join_date']->format('M Y')); ?>

                        </span>
                    </div>
                </div>
                
                <div class="profile-stats">
                    <div class="stat-card">
                        <span class="stat-number"><?php echo e(number_format($stats['total_points'])); ?></span>
                        <span class="stat-label">Points</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number"><?php echo e($stats['level']); ?></span>
                        <span class="stat-label">Level</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number"><?php echo e($stats['completed_lessons']); ?></span>
                        <span class="stat-label">Lessons</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number"><?php echo e($stats['completed_tasks']); ?></span>
                        <span class="stat-label">Tasks</span>
                    </div>
                </div>
            </div>
            
            <?php if($user->id === Auth::id()): ?>
                <div class="profile-actions">
                    <a href="<?php echo e(route('profile.edit')); ?>" class="btn-edit">
                        <i class="fas fa-edit"></i>
                        Edit Profile
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Profile Content -->
    <div class="profile-content">
        <!-- Campus Progress -->
        <div class="content-section">
            <h2>Campus Progress</h2>
            <div class="campus-progress-grid">
                <?php $__currentLoopData = $enrolledCampuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $campus): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="campus-progress-card">
                        <div class="campus-header">
                            <div class="campus-icon" style="color: <?php echo e($campus->color); ?>">
                                <i class="<?php echo e($campus->icon); ?>"></i>
                            </div>
                            <div class="campus-info">
                                <h3><?php echo e($campus->name); ?></h3>
                                <p><?php echo e($campus->completed_lessons); ?>/<?php echo e($campus->total_lessons); ?> lessons</p>
                            </div>
                        </div>
                        
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: <?php echo e($campus->progress_percentage); ?>%; background: <?php echo e($campus->color); ?>"></div>
                        </div>
                        <span class="progress-text"><?php echo e($campus->progress_percentage); ?>% Complete</span>
                        
                        <a href="<?php echo e(route('campuses.show', $campus)); ?>" class="campus-link">
                            View Campus
                        </a>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                
                <?php if($enrolledCampuses->isEmpty()): ?>
                    <div class="empty-state">
                        <i class="fas fa-university"></i>
                        <h3>No Campuses Yet</h3>
                        <p>Start your learning journey by enrolling in a campus.</p>
                        <a href="<?php echo e(route('campuses.index')); ?>" class="btn-primary">Browse Campuses</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Achievements -->
        <div class="content-section">
            <h2>Achievements</h2>
            <div class="achievements-grid">
                <?php $__currentLoopData = $achievements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $achievement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="achievement-card <?php echo e($achievement['earned'] ? 'earned' : 'locked'); ?>">
                        <div class="achievement-icon"><?php echo e($achievement['icon']); ?></div>
                        <div class="achievement-info">
                            <h4><?php echo e($achievement['title']); ?></h4>
                            <p><?php echo e($achievement['description']); ?></p>
                            
                            <?php if($achievement['earned']): ?>
                                <span class="earned-date">
                                    Earned <?php echo e($achievement['earned_at']->format('M d, Y')); ?>

                                </span>
                            <?php else: ?>
                                <?php if(isset($achievement['progress'])): ?>
                                    <div class="achievement-progress">
                                        <div class="progress-bar small">
                                            <div class="progress-fill" style="width: <?php echo e(($achievement['progress'] / $achievement['target']) * 100); ?>%"></div>
                                        </div>
                                        <span class="progress-text"><?php echo e($achievement['progress']); ?>/<?php echo e($achievement['target']); ?></span>
                                    </div>
                                <?php else: ?>
                                    <span class="locked-text">🔒 Locked</span>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="content-section">
            <h2>Recent Activity</h2>
            <div class="activity-timeline">
                <?php $__currentLoopData = $recentActivity; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="<?php echo e($activity['icon']); ?>"></i>
                        </div>
                        <div class="activity-content">
                            <h4><?php echo e($activity['title']); ?></h4>
                            <p><?php echo e($activity['description']); ?></p>
                            <span class="activity-date"><?php echo e($activity['date']->diffForHumans()); ?></span>
                        </div>
                        <div class="activity-points">
                            +<?php echo e($activity['points']); ?> pts
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                
                <?php if($recentActivity->isEmpty()): ?>
                    <div class="empty-state">
                        <i class="fas fa-clock"></i>
                        <h3>No Recent Activity</h3>
                        <p>Start learning to see your activity here.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
.profile-page {
    max-width: 1200px;
    margin: 0 auto;
}

.profile-header {
    position: relative;
    margin-bottom: 40px;
}

.profile-banner {
    height: 200px;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%);
    border-radius: 20px;
    position: relative;
    overflow: hidden;
}

.banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.1) 100%);
}

.profile-info {
    display: flex;
    align-items: flex-end;
    gap: 30px;
    margin-top: -60px;
    position: relative;
    z-index: 1;
    padding: 0 30px;
}

.profile-avatar {
    position: relative;
    flex-shrink: 0;
}

.profile-avatar img,
.default-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 5px solid #ffffff;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.default-avatar {
    background: linear-gradient(135deg, #666 0%, #888 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 3rem;
}

.avatar-edit {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 35px;
    height: 35px;
    background: #FFD700;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000000;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 3px solid #ffffff;
}

.avatar-edit:hover {
    background: #FFA500;
    transform: scale(1.1);
}

.profile-details {
    flex: 1;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.profile-main h1 {
    color: #FFD700;
    font-size: 2.2rem;
    margin-bottom: 8px;
}

.username {
    color: #cccccc;
    font-size: 1.1rem;
    margin-bottom: 10px;
}

.bio {
    color: #ffffff;
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 15px;
}

.profile-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #cccccc;
    font-size: 0.9rem;
}

.meta-item i {
    color: #FFD700;
}

.meta-item a {
    color: #FFD700;
    text-decoration: none;
}

.meta-item a:hover {
    text-decoration: underline;
}

.profile-stats {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.stat-card {
    text-align: center;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-width: 80px;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: #FFD700;
}

.stat-label {
    color: #cccccc;
    font-size: 0.8rem;
}

.profile-actions {
    align-self: flex-start;
    margin-top: 20px;
}

.btn-edit {
    background: rgba(255, 215, 0, 0.2);
    color: #FFD700;
    border: 1px solid #FFD700;
    padding: 12px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-edit:hover {
    background: #FFD700;
    color: #000000;
    transform: translateY(-2px);
}

.profile-content {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.content-section h2 {
    color: #FFD700;
    font-size: 1.8rem;
    margin-bottom: 25px;
}

.campus-progress-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.campus-progress-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    transition: all 0.3s ease;
}

.campus-progress-card:hover {
    transform: translateY(-5px);
    border-color: rgba(255, 215, 0, 0.5);
}

.campus-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.campus-icon {
    font-size: 2rem;
}

.campus-info h3 {
    color: #ffffff;
    margin-bottom: 5px;
}

.campus-info p {
    color: #cccccc;
    font-size: 0.9rem;
}

.progress-bar {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    height: 8px;
    margin-bottom: 10px;
    overflow: hidden;
}

.progress-bar.small {
    height: 6px;
}

.progress-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

.progress-text {
    color: #cccccc;
    font-size: 0.9rem;
    margin-bottom: 15px;
    display: block;
}

.campus-link {
    color: #FFD700;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
}

.campus-link:hover {
    text-decoration: underline;
}

.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.achievement-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
}

.achievement-card.earned {
    border-color: #FFD700;
    background: rgba(255, 215, 0, 0.1);
}

.achievement-card.locked {
    opacity: 0.6;
}

.achievement-card:hover {
    transform: translateY(-3px);
}

.achievement-icon {
    font-size: 2.5rem;
    flex-shrink: 0;
}

.achievement-info h4 {
    color: #ffffff;
    margin-bottom: 5px;
}

.achievement-info p {
    color: #cccccc;
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.earned-date {
    color: #FFD700;
    font-size: 0.8rem;
    font-weight: 500;
}

.locked-text {
    color: #666;
    font-size: 0.8rem;
}

.achievement-progress {
    margin-top: 10px;
}

.achievement-progress .progress-text {
    font-size: 0.8rem;
    margin-bottom: 0;
    margin-top: 5px;
}

.activity-timeline {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.activity-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.activity-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 215, 0, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFD700;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    color: #ffffff;
    margin-bottom: 5px;
}

.activity-content p {
    color: #cccccc;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.activity-date {
    color: #999;
    font-size: 0.8rem;
}

.activity-points {
    color: #FFD700;
    font-weight: bold;
    font-size: 0.9rem;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #cccccc;
}

.empty-state i {
    font-size: 4rem;
    color: #666;
    margin-bottom: 20px;
}

.empty-state h3 {
    color: #ffffff;
    margin-bottom: 10px;
}

.btn-primary {
    background: #FFD700;
    color: #000000;
    padding: 12px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    display: inline-block;
    margin-top: 15px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: #FFA500;
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .profile-info {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 20px;
        padding: 0 20px;
    }
    
    .profile-stats {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .campus-progress-grid,
    .achievements-grid {
        grid-template-columns: 1fr;
    }
    
    .activity-item {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .profile-meta {
        justify-content: center;
    }
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\New folder\the-real-world\resources\views/profile/show.blade.php ENDPATH**/ ?>