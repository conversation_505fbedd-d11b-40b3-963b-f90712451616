@extends('layouts.admin')

@section('title', 'Task Submissions Review')

@section('content')
<div class="task-submissions">
    <!-- Page Header -->
    <div class="page-header">
        <div class="header-content">
            <h1>Task Submissions Review</h1>
            <p>Review and approve student task submissions</p>
        </div>
        <div class="header-stats">
            <div class="stat-item">
                <span class="stat-number">{{ $submissions->where('status', 'pending')->count() }}</span>
                <span class="stat-label">Pending</span>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filters-section">
        <form method="GET" class="filters-form">
            <div class="filter-group">
                <select name="status" class="form-control">
                    <option value="">All Status</option>
                    <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                    <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>Approved</option>
                    <option value="rejected" {{ request('status') === 'rejected' ? 'selected' : '' }}>Rejected</option>
                </select>
            </div>
            <div class="filter-group">
                <select name="campus" class="form-control">
                    <option value="">All Campuses</option>
                    @foreach($campuses as $campus)
                        <option value="{{ $campus->id }}" {{ request('campus') == $campus->id ? 'selected' : '' }}>
                            {{ $campus->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <button type="submit" class="btn btn-secondary">
                <i class="fas fa-filter"></i>
                Filter
            </button>
            <a href="{{ route('admin.task-submissions') }}" class="btn btn-secondary">
                <i class="fas fa-times"></i>
                Clear
            </a>
        </form>
    </div>

    <!-- Submissions Grid -->
    <div class="submissions-grid">
        @foreach($submissions as $submission)
            <div class="submission-card {{ $submission->status }}">
                <div class="submission-header">
                    <div class="task-info">
                        <h3>{{ $submission->dailyTask->title }}</h3>
                        <p class="campus-name">{{ $submission->dailyTask->campus->name }}</p>
                        <span class="task-difficulty {{ $submission->dailyTask->difficulty }}">
                            {{ ucfirst($submission->dailyTask->difficulty) }}
                        </span>
                    </div>
                    <div class="submission-status">
                        <span class="status-badge {{ $submission->status }}">
                            {{ ucfirst($submission->status) }}
                        </span>
                        <span class="submission-date">{{ $submission->created_at->diffForHumans() }}</span>
                    </div>
                </div>

                <div class="student-info">
                    <div class="student-avatar">
                        @if($submission->user->avatar)
                            <img src="{{ Storage::url($submission->user->avatar) }}" alt="{{ $submission->user->name }}">
                        @else
                            <div class="default-avatar">{{ substr($submission->user->name, 0, 1) }}</div>
                        @endif
                    </div>
                    <div class="student-details">
                        <h4>{{ $submission->user->name }}</h4>
                        <p>{{ $submission->user->email }}</p>
                        <span class="student-level">Level {{ $submission->user->level }}</span>
                    </div>
                </div>

                <div class="submission-content">
                    <h4>Submission:</h4>
                    <p>{{ Str::limit($submission->submission_content, 200) }}</p>
                    
                    @if($submission->attachments && count($submission->attachments) > 0)
                        <div class="attachments">
                            <h5>Attachments:</h5>
                            @foreach($submission->attachments as $attachment)
                                <div class="attachment-item">
                                    <i class="fas fa-file"></i>
                                    <span>{{ $attachment['name'] ?? 'File' }}</span>
                                    <a href="{{ Storage::url($attachment['path']) }}" target="_blank" class="view-attachment">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>

                @if($submission->feedback)
                    <div class="feedback-section">
                        <h4>Feedback:</h4>
                        <p>{{ $submission->feedback }}</p>
                        @if($submission->points_awarded)
                            <span class="points-awarded">+{{ $submission->points_awarded }} points awarded</span>
                        @endif
                    </div>
                @endif

                <div class="submission-actions">
                    @if($submission->status === 'pending')
                        <button class="btn btn-success" onclick="reviewSubmission({{ $submission->id }}, 'approved')">
                            <i class="fas fa-check"></i>
                            Approve
                        </button>
                        <button class="btn btn-danger" onclick="reviewSubmission({{ $submission->id }}, 'rejected')">
                            <i class="fas fa-times"></i>
                            Reject
                        </button>
                    @else
                        <button class="btn btn-secondary" onclick="viewSubmissionDetails({{ $submission->id }})">
                            <i class="fas fa-eye"></i>
                            View Details
                        </button>
                        @if($submission->status !== 'pending')
                            <button class="btn btn-primary" onclick="reviewSubmission({{ $submission->id }}, 'pending')">
                                <i class="fas fa-redo"></i>
                                Re-review
                            </button>
                        @endif
                    @endif
                </div>
            </div>
        @endforeach
    </div>

    @if($submissions->isEmpty())
        <div class="empty-state">
            <i class="fas fa-clipboard-check"></i>
            <h3>No Submissions Found</h3>
            <p>No task submissions match your current filters.</p>
        </div>
    @endif

    <!-- Pagination -->
    @if($submissions->hasPages())
        <div class="pagination-wrapper">
            {{ $submissions->appends(request()->query())->links() }}
        </div>
    @endif
</div>

<!-- Review Modal -->
<div id="reviewModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Review Submission</h3>
            <button class="modal-close" onclick="closeReviewModal()">&times;</button>
        </div>
        <form id="reviewForm" method="POST">
            @csrf
            @method('PUT')
            <div class="modal-body">
                <div class="form-group">
                    <label for="review_status">Status</label>
                    <select id="review_status" name="status" class="form-control" required>
                        <option value="approved">Approved</option>
                        <option value="rejected">Rejected</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="review_feedback">Feedback</label>
                    <textarea id="review_feedback" name="feedback" class="form-control" rows="4" placeholder="Provide feedback to the student..."></textarea>
                </div>
                <div class="form-group" id="pointsGroup">
                    <label for="review_points">Points to Award</label>
                    <input type="number" id="review_points" name="points_awarded" class="form-control" min="0" max="100">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeReviewModal()">Cancel</button>
                <button type="submit" class="btn btn-primary">Submit Review</button>
            </div>
        </form>
    </div>
</div>

<style>
.task-submissions {
    max-width: 1400px;
    margin: 0 auto;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-content h1 {
    color: #FFD700;
    font-size: 2rem;
    margin-bottom: 5px;
}

.header-content p {
    color: #cccccc;
    font-size: 1rem;
}

.header-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    text-align: center;
    background: rgba(244, 67, 54, 0.1);
    border: 1px solid rgba(244, 67, 54, 0.3);
    border-radius: 10px;
    padding: 15px 20px;
}

.stat-number {
    display: block;
    color: #f44336;
    font-size: 1.5rem;
    font-weight: bold;
}

.stat-label {
    color: #f44336;
    font-size: 0.8rem;
    text-transform: uppercase;
}

.filters-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.filters-form {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.submissions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.submission-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 25px;
    transition: all 0.3s ease;
}

.submission-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.submission-card.pending {
    border-color: rgba(255, 152, 0, 0.5);
    background: rgba(255, 152, 0, 0.05);
}

.submission-card.approved {
    border-color: rgba(76, 175, 80, 0.5);
    background: rgba(76, 175, 80, 0.05);
}

.submission-card.rejected {
    border-color: rgba(244, 67, 54, 0.5);
    background: rgba(244, 67, 54, 0.05);
}

.submission-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.task-info h3 {
    color: #FFD700;
    margin-bottom: 5px;
    font-size: 1.2rem;
}

.campus-name {
    color: #cccccc;
    font-size: 0.9rem;
    margin-bottom: 8px;
}

.task-difficulty {
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: bold;
    text-transform: uppercase;
}

.task-difficulty.easy { background: rgba(76, 175, 80, 0.2); color: #4CAF50; }
.task-difficulty.medium { background: rgba(255, 152, 0, 0.2); color: #FF9800; }
.task-difficulty.hard { background: rgba(244, 67, 54, 0.2); color: #f44336; }

.submission-status {
    text-align: right;
}

.status-badge {
    padding: 5px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
    display: block;
    margin-bottom: 5px;
}

.status-badge.pending { background: rgba(255, 152, 0, 0.2); color: #FF9800; }
.status-badge.approved { background: rgba(76, 175, 80, 0.2); color: #4CAF50; }
.status-badge.rejected { background: rgba(244, 67, 54, 0.2); color: #f44336; }

.submission-date {
    color: #999;
    font-size: 0.8rem;
}

.student-info {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
}

.student-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #FFD700;
    flex-shrink: 0;
}

.student-avatar img,
.default-avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.default-avatar {
    background: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.2rem;
}

.student-details h4 {
    color: #ffffff;
    margin-bottom: 3px;
    font-size: 1rem;
}

.student-details p {
    color: #cccccc;
    font-size: 0.8rem;
    margin-bottom: 3px;
}

.student-level {
    color: #FFD700;
    font-size: 0.8rem;
    font-weight: bold;
}

.submission-content {
    margin-bottom: 20px;
}

.submission-content h4 {
    color: #FFD700;
    margin-bottom: 10px;
    font-size: 1rem;
}

.submission-content p {
    color: #cccccc;
    line-height: 1.6;
    margin-bottom: 15px;
}

.attachments {
    margin-top: 15px;
}

.attachments h5 {
    color: #FFD700;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.attachment-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    margin-bottom: 5px;
}

.attachment-item i {
    color: #FFD700;
}

.attachment-item span {
    flex: 1;
    color: #cccccc;
    font-size: 0.8rem;
}

.view-attachment {
    color: #FFD700;
    text-decoration: none;
}

.feedback-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

.feedback-section h4 {
    color: #FFD700;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.feedback-section p {
    color: #cccccc;
    margin-bottom: 8px;
    line-height: 1.5;
}

.points-awarded {
    color: #4CAF50;
    font-weight: bold;
    font-size: 0.8rem;
}

.submission-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.submission-actions .btn {
    flex: 1;
    min-width: 120px;
    justify-content: center;
}

.empty-state {
    text-align: center;
    padding: 80px 20px;
    color: #666;
}

.empty-state i {
    font-size: 5rem;
    margin-bottom: 25px;
    color: #444;
}

.empty-state h3 {
    color: #ffffff;
    margin-bottom: 10px;
    font-size: 1.5rem;
}

.pagination-wrapper {
    margin-top: 30px;
    display: flex;
    justify-content: center;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: #1a1a1a;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
    color: #FFD700;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: #cccccc;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

@media (max-width: 768px) {
    .submissions-grid {
        grid-template-columns: 1fr;
    }
    
    .submission-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .student-info {
        flex-direction: column;
        text-align: center;
    }
    
    .submission-actions {
        flex-direction: column;
    }
    
    .filters-form {
        flex-direction: column;
    }
    
    .filter-group {
        min-width: auto;
    }
}
</style>

<script>
function reviewSubmission(submissionId, status) {
    const modal = document.getElementById('reviewModal');
    const form = document.getElementById('reviewForm');
    const statusSelect = document.getElementById('review_status');
    const pointsGroup = document.getElementById('pointsGroup');
    
    form.action = `/admin/task-submissions/${submissionId}/review`;
    statusSelect.value = status;
    
    // Show/hide points field based on status
    pointsGroup.style.display = status === 'approved' ? 'block' : 'none';
    
    modal.style.display = 'flex';
}

function closeReviewModal() {
    document.getElementById('reviewModal').style.display = 'none';
}

function viewSubmissionDetails(submissionId) {
    // In a real implementation, this would open a detailed view
    alert('Detailed view would be implemented here');
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modal = document.getElementById('reviewModal');
    if (event.target === modal) {
        closeReviewModal();
    }
});

// Toggle points field based on status selection
document.getElementById('review_status').addEventListener('change', function() {
    const pointsGroup = document.getElementById('pointsGroup');
    pointsGroup.style.display = this.value === 'approved' ? 'block' : 'none';
});
</script>
@endsection
