<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Campus;
use App\Models\DailyTask;
use App\Models\TaskSubmission;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class TaskController extends Controller
{
    /**
     * Display daily tasks for a campus.
     */
    public function index(Campus $campus)
    {
        // Check if user is enrolled in the campus
        if (!Auth::user()->campuses()->where('campus_id', $campus->id)->exists()) {
            return redirect()->route('campuses.enroll', $campus)->with('error', 'You need to enroll in this campus first.');
        }

        $todayTask = $campus->dailyTasks()->today()->active()->first();
        $recentTasks = $campus->dailyTasks()
            ->active()
            ->where('task_date', '<', today())
            ->orderBy('task_date', 'desc')
            ->limit(7)
            ->get();

        // Get user's submissions for these tasks
        $taskIds = collect([$todayTask])->merge($recentTasks)->filter()->pluck('id');
        $userSubmissions = Auth::user()->taskSubmissions()
            ->whereIn('daily_task_id', $taskIds)
            ->get()
            ->keyBy('daily_task_id');

        return view('tasks.index', compact('campus', 'todayTask', 'recentTasks', 'userSubmissions'));
    }

    /**
     * Show task submission form.
     */
    public function show(Campus $campus, DailyTask $task)
    {
        // Check if user is enrolled in the campus
        if (!Auth::user()->campuses()->where('campus_id', $campus->id)->exists()) {
            return redirect()->route('campuses.enroll', $campus)->with('error', 'You need to enroll in this campus first.');
        }

        // Check if task belongs to campus
        if ($task->campus_id !== $campus->id) {
            abort(404);
        }

        // Get user's existing submission
        $submission = Auth::user()->taskSubmissions()->where('daily_task_id', $task->id)->first();

        return view('tasks.show', compact('campus', 'task', 'submission'));
    }

    /**
     * Submit a task.
     */
    public function submit(Request $request, Campus $campus, DailyTask $task)
    {
        // Check if user is enrolled in the campus
        if (!Auth::user()->campuses()->where('campus_id', $campus->id)->exists()) {
            return response()->json(['error' => 'Not enrolled in campus'], 403);
        }

        // Check if task belongs to campus
        if ($task->campus_id !== $campus->id) {
            return response()->json(['error' => 'Invalid task'], 404);
        }

        $request->validate([
            'submission_content' => 'required|string|max:2000',
            'attachments.*' => 'file|max:10240|mimes:jpg,jpeg,png,pdf,doc,docx,txt',
        ]);

        // Check if user already submitted
        $existingSubmission = Auth::user()->taskSubmissions()->where('daily_task_id', $task->id)->first();
        if ($existingSubmission) {
            return response()->json(['error' => 'You have already submitted this task'], 400);
        }

        $attachments = [];
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $path = $file->store('task-submissions', 'public');
                $attachments[] = [
                    'name' => $file->getClientOriginalName(),
                    'path' => $path,
                    'size' => $file->getSize(),
                    'type' => $file->getMimeType(),
                ];
            }
        }

        $submission = TaskSubmission::create([
            'daily_task_id' => $task->id,
            'user_id' => Auth::id(),
            'submission_content' => $request->submission_content,
            'attachments' => $attachments,
            'status' => 'pending',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Task submitted successfully! You will receive feedback soon.',
            'submission' => $submission,
        ]);
    }

    /**
     * Get user's task submissions.
     */
    public function submissions(Campus $campus)
    {
        // Check if user is enrolled in the campus
        if (!Auth::user()->campuses()->where('campus_id', $campus->id)->exists()) {
            return redirect()->route('campuses.enroll', $campus)->with('error', 'You need to enroll in this campus first.');
        }

        $submissions = Auth::user()->taskSubmissions()
            ->whereHas('dailyTask', function ($query) use ($campus) {
                $query->where('campus_id', $campus->id);
            })
            ->with('dailyTask')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('tasks.submissions', compact('campus', 'submissions'));
    }

    /**
     * Show leaderboard for task completions.
     */
    public function leaderboard(Campus $campus)
    {
        // Check if user is enrolled in the campus
        if (!Auth::user()->campuses()->where('campus_id', $campus->id)->exists()) {
            return redirect()->route('campuses.enroll', $campus)->with('error', 'You need to enroll in this campus first.');
        }

        // Get top users by approved task submissions in this campus
        $topUsers = Auth::user()->newQuery()
            ->select('users.*')
            ->selectRaw('COUNT(task_submissions.id) as completed_tasks')
            ->selectRaw('SUM(task_submissions.points_awarded) as total_points')
            ->join('task_submissions', 'users.id', '=', 'task_submissions.user_id')
            ->join('daily_tasks', 'task_submissions.daily_task_id', '=', 'daily_tasks.id')
            ->where('daily_tasks.campus_id', $campus->id)
            ->where('task_submissions.status', 'approved')
            ->groupBy('users.id')
            ->orderBy('total_points', 'desc')
            ->orderBy('completed_tasks', 'desc')
            ->limit(50)
            ->get();

        // Get current user's rank
        $userRank = null;
        $userStats = null;
        foreach ($topUsers as $index => $user) {
            if ($user->id === Auth::id()) {
                $userRank = $index + 1;
                $userStats = $user;
                break;
            }
        }

        return view('tasks.leaderboard', compact('campus', 'topUsers', 'userRank', 'userStats'));
    }
}
