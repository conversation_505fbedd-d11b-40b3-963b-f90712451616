<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title'); ?> - Admin Panel</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .admin-layout {
            display: grid;
            grid-template-columns: 280px 1fr;
            min-height: 100vh;
        }

        /* Admin Sidebar */
        .admin-sidebar {
            background: rgba(0, 0, 0, 0.8);
            border-right: 1px solid rgba(255, 215, 0, 0.2);
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            width: 280px;
            overflow-y: auto;
        }

        .admin-logo {
            text-align: center;
            padding: 0 20px 30px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 30px;
        }

        .admin-logo h1 {
            color: #FFD700;
            font-size: 1.5rem;
            margin-bottom: 5px;
        }

        .admin-logo p {
            color: #cccccc;
            font-size: 0.8rem;
        }

        .admin-nav {
            padding: 0 10px;
        }

        .nav-section {
            margin-bottom: 25px;
        }

        .section-title {
            color: #999;
            font-size: 0.7rem;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            padding: 0 15px 10px;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-item a {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 15px;
            color: #cccccc;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .nav-item a:hover,
        .nav-item.active a {
            background: rgba(255, 215, 0, 0.1);
            color: #FFD700;
            transform: translateX(5px);
        }

        .nav-item i {
            width: 20px;
            text-align: center;
            font-size: 1rem;
        }

        /* Main Content */
        .admin-main {
            margin-left: 280px;
            min-height: 100vh;
        }

        .admin-header {
            background: rgba(255, 255, 255, 0.05);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
            backdrop-filter: blur(10px);
        }

        .header-left h2 {
            color: #FFD700;
            font-size: 1.3rem;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .admin-user {
            display: flex;
            align-items: center;
            gap: 10px;
            position: relative;
        }

        .admin-user-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: #FFD700;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #000000;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .admin-user-info {
            display: flex;
            flex-direction: column;
        }

        .admin-user-name {
            color: #ffffff;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .admin-user-role {
            color: #FFD700;
            font-size: 0.7rem;
            text-transform: uppercase;
        }

        .admin-content {
            padding: 30px;
        }

        /* Notifications */
        .notification {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .notification.success {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid rgba(76, 175, 80, 0.3);
            color: #4CAF50;
        }

        .notification.error {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid rgba(244, 67, 54, 0.3);
            color: #f44336;
        }

        .notification.warning {
            background: rgba(255, 152, 0, 0.1);
            border: 1px solid rgba(255, 152, 0, 0.3);
            color: #FF9800;
        }

        .notification.info {
            background: rgba(33, 150, 243, 0.1);
            border: 1px solid rgba(33, 150, 243, 0.3);
            color: #2196F3;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .admin-layout {
                grid-template-columns: 1fr;
            }

            .admin-sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                z-index: 1000;
            }

            .admin-sidebar.open {
                transform: translateX(0);
            }

            .admin-main {
                margin-left: 0;
            }

            .mobile-menu-btn {
                display: block;
                background: none;
                border: none;
                color: #FFD700;
                font-size: 1.2rem;
                cursor: pointer;
            }
        }

        .mobile-menu-btn {
            display: none;
        }

        /* Utility Classes */
        .btn {
            padding: 10px 20px;
            border-radius: 20px;
            text-decoration: none;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: #FFD700;
            color: #000000;
        }

        .btn-primary:hover {
            background: #FFA500;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .btn-danger {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
            border: 1px solid #f44336;
        }

        .btn-danger:hover {
            background: #f44336;
            color: #ffffff;
        }

        .btn-success {
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
            border: 1px solid #4CAF50;
        }

        .btn-success:hover {
            background: #4CAF50;
            color: #ffffff;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            overflow: hidden;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .table th {
            background: rgba(255, 255, 255, 0.1);
            color: #FFD700;
            font-weight: bold;
        }

        .table td {
            color: #ffffff;
        }

        .table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            color: #ffffff;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: #ffffff;
            font-size: 0.9rem;
        }

        .form-control:focus {
            outline: none;
            border-color: #FFD700;
            background: rgba(255, 255, 255, 0.15);
        }

        .card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }

        .card-header {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .card-title {
            color: #FFD700;
            font-size: 1.2rem;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Admin Sidebar -->
        <aside class="admin-sidebar" id="adminSidebar">
            <div class="admin-logo">
                <h1>TRW Admin</h1>
                <p>Management Panel</p>
            </div>

            <nav class="admin-nav">
                <div class="nav-section">
                    <div class="section-title">Dashboard</div>
                    <div class="nav-item <?php echo e(request()->routeIs('admin.dashboard') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.dashboard')); ?>">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Overview</span>
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="section-title">User Management</div>
                    <div class="nav-item <?php echo e(request()->routeIs('admin.users*') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.users')); ?>">
                            <i class="fas fa-users"></i>
                            <span>All Users</span>
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="section-title">Content</div>
                    <div class="nav-item <?php echo e(request()->routeIs('admin.content*') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.content')); ?>">
                            <i class="fas fa-edit"></i>
                            <span>Content Management</span>
                        </a>
                    </div>
                    <div class="nav-item <?php echo e(request()->routeIs('admin.task-submissions*') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.task-submissions')); ?>">
                            <i class="fas fa-clipboard-check"></i>
                            <span>Task Reviews</span>
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="section-title">Platform</div>
                    <div class="nav-item">
                        <a href="<?php echo e(route('leaderboard.index')); ?>">
                            <i class="fas fa-trophy"></i>
                            <span>Leaderboard</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="<?php echo e(route('dashboard')); ?>">
                            <i class="fas fa-external-link-alt"></i>
                            <span>View Platform</span>
                        </a>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Admin Header -->
            <header class="admin-header">
                <div class="header-left">
                    <button class="mobile-menu-btn" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h2><?php echo $__env->yieldContent('title', 'Admin Panel'); ?></h2>
                </div>
                <div class="header-right">
                    <div class="admin-user">
                        <div class="admin-user-avatar">
                            <?php echo e(substr(Auth::user()->name, 0, 1)); ?>

                        </div>
                        <div class="admin-user-info">
                            <span class="admin-user-name"><?php echo e(Auth::user()->name); ?></span>
                            <span class="admin-user-role">Administrator</span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Admin Content -->
            <div class="admin-content">
                <!-- Flash Messages -->
                <?php if(session('success')): ?>
                    <div class="notification success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo e(session('success')); ?>

                    </div>
                <?php endif; ?>

                <?php if(session('error')): ?>
                    <div class="notification error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo e(session('error')); ?>

                    </div>
                <?php endif; ?>

                <?php if(session('warning')): ?>
                    <div class="notification warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo e(session('warning')); ?>

                    </div>
                <?php endif; ?>

                <?php if(session('info')): ?>
                    <div class="notification info">
                        <i class="fas fa-info-circle"></i>
                        <?php echo e(session('info')); ?>

                    </div>
                <?php endif; ?>

                <?php echo $__env->yieldContent('content'); ?>
            </div>
        </main>
    </div>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('adminSidebar');
            sidebar.classList.toggle('open');
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('adminSidebar');
            const menuBtn = document.querySelector('.mobile-menu-btn');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(event.target) && 
                !menuBtn.contains(event.target)) {
                sidebar.classList.remove('open');
            }
        });

        // Auto-hide notifications after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const notifications = document.querySelectorAll('.notification');
            notifications.forEach(notification => {
                setTimeout(() => {
                    notification.style.opacity = '0';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }, 5000);
            });
        });
    </script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\New folder\the-real-world\resources\views/layouts/admin.blade.php ENDPATH**/ ?>