<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Campus extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'color',
        'background_image',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the courses for the campus.
     */
    public function courses(): HasMany
    {
        return $this->hasMany(Course::class);
    }

    /**
     * Get the chat rooms for the campus.
     */
    public function chatRooms(): HasMany
    {
        return $this->hasMany(ChatRoom::class);
    }

    /**
     * Get the daily tasks for the campus.
     */
    public function dailyTasks(): HasMany
    {
        return $this->hasMany(DailyTask::class);
    }

    /**
     * Get the users enrolled in this campus.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_campus_enrollments')
                    ->withPivot('enrolled_at', 'is_active')
                    ->withTimestamps();
    }

    /**
     * Scope a query to only include active campuses.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }
}
