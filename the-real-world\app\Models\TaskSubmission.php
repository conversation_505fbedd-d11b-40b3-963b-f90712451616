<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TaskSubmission extends Model
{
    use HasFactory;

    protected $fillable = [
        'daily_task_id',
        'user_id',
        'submission_content',
        'attachments',
        'status',
        'feedback',
        'points_awarded',
        'reviewed_at',
        'reviewed_by',
    ];

    protected $casts = [
        'daily_task_id' => 'integer',
        'user_id' => 'integer',
        'attachments' => 'array',
        'points_awarded' => 'integer',
        'reviewed_at' => 'datetime',
        'reviewed_by' => 'integer',
    ];

    /**
     * Get the daily task that owns the submission.
     */
    public function dailyTask(): BelongsTo
    {
        return $this->belongsTo(DailyTask::class);
    }

    /**
     * Get the user that owns the submission.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who reviewed the submission.
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Scope a query to only include pending submissions.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include approved submissions.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }
}
